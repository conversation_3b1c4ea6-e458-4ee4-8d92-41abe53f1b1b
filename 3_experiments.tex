\section{Experiments}
\begin{table*}
	[t]
	\centering
	\caption{Accuracy of Embodied-R and baselines on 8 indoor and outdoor embodied spatial reasoning tasks. The baselines include popular proprietary models, state-of-the-art (SOTA) multimodal reasoning models, open-sourced video-large language models, and models fine-tuned on the same training dataset.}
	\label{tab:acc}
	\setlength{\tabcolsep}{3pt} 
	\begin{tabular}{@{}c@{}} 
		\begin{minipage}[c]{0.58\textwidth} 
\centering \begin{tabular}{r|c|cccccccc}\hline & \multicolumn{1}{l|}{} & \multicolumn{4}{c}{\cellcolor{cyan!20}UrbanVideo-Bench} & \multicolumn{4}{c}{\cellcolor{green!20}VSI-Bench} \\Method &\multicolumn{1}{l|}{Avg.} & \rotatebox{90}{\textit{Landmark Position}} & \rotatebox{90}{\textit{Counterfactual}} & \rotatebox{90}{\textit{Progress Evaluation} } & \rotatebox{90}{\textit{Action Generation}} & \rotatebox{90}{\textit{Relative Distance}} & \rotatebox{90}{\textit{Relative Direction}} & \rotatebox{90}{\textit{Route Planning}} & \rotatebox{90}{\textit{Appearance Order}} \\ \hline Random & 24.0 & 19.7 &25.0 & 21.8 & 16.4 & 25.0 & 36.1 & 28.3 & 25.0 \\ \hline \multicolumn{1}{l|}{\cellcolor[HTML]{F5F5F5}\textit{\textbf{Proprietary Models (API)}}} & \cellcolor[HTML]{F5F5F5} & \multicolumn{8}{c}{\cellcolor[HTML]{F5F5F5}} \\ Qwen-VL-Max[32f] & 34.1 & 44.8 & 49.2 & 38.8 & 29.6 & 28.0 & 33.3 & 29.6 & 28.3\\ GPT-4o[32f] & 35.7 & 36.8 & 44.7 & 34.2 & 33.8 & 37.0 & 41.3 & 31.5 & 28.5\\ Gemini-1.5-Flash[1fps] & 38.3 & 37.8 & 42.4 & 43.3 & 34.4 & 37.7 & 41.0 & 31.5 & 37.8\\ Gemini-1.5-Pro[1fps] & 39.7 & 37.4 & 46.2 & 38.8 & 31.9 & 51.3 & 46.3& 36.0 & 34.6\\ \hline \multicolumn{1}{l|}{\cellcolor[HTML]{F5F5F5}\textit{\textbf{SOTA Reasoning Models (API)}}} & \cellcolor[HTML]{F5F5F5} & \multicolumn{8}{c}{\cellcolor[HTML]{F5F5F5}} \\ OpenAI-o1[32f] & 37.2 & 34.6 & 53.3 & 39.1 & 28.0 & 39.7 & 35.8 & 52.9 & 39.8\\ Gemini-2.5-Pro[1fps] & 40.8 & 40.0 & 75.0 & 38.7 & 23.5 & 42.0 & 34.5 & 52.4 & 63.6 \\ \hline \multicolumn{1}{l|}{\cellcolor[HTML]{F5F5F5}\textit{\textbf{Open-source Models}}} & \cellcolor[HTML]{F5F5F5} & \multicolumn{8}{c}{\cellcolor[HTML]{F5F5F5}} \\ LLaVA-NeXT-Video-7B-hf[32f] & 29.5 & 49.5 & 20.5 & 36.6 & 19.2 & 25.2 & 26.3 & 29.9 & 24.5\\ Phi-3.5-vision-instruct[32f] & 29.0 & 49.2 & 34.8 & 33.2 & 15.6 & 25.4 & 26.5 & 36.9 & 25.2 \\ Kangaroo[64f] & 30.0 & 35.5 & 42.4 & 32.5 & 32.4 & 25.2 & 26.8 & 23.5 & 24.9\\ InternVL2-2B[32] & 24.5 & 19.3 & 45.5 & 29.2 & 20.9 & 25.1 & 25.0 & 32.6 & 23.9\\ InternVL2-8B[32f] & 25.5 &23.1 & 45.5 & 31.5 & 21.4 & 24.7 & 25.7 & 28.3 & 24.8 \\ InternVL2-40B[32f] & 25.8 & 23.2 & 41.7 & 32.4 & 22.3 & 24.9 & 25.7 & 29.4 & 24.5 \\ Qwen2.5-VL-3B-Instruct[1fps] & 33.1 & 32.1 & 47.8 & 34.0 & 31.0 & 27.9 & 32.6 & 39.0 & 38.9 \\ Qwen2.5-VL-7B-Instruct[1fps] & 33.3 & 33.3 & 21.7 & 25.0 & 27.8 & 35.8 & 39.7 & 48.8 & 38.8 \\
Qwen2.5-VL-72B-Instruct[1fps] & 34.9 & 34.7 & 34.8 & 26.4 & 37.7 & 40.8 & 29.0 & 32.5 & 43.9 \\ \hline
\multicolumn{1}{l|}{\cellcolor[HTML]{F5F5F5}\textit{\textbf{Supervised Fine-Tuning}}} & \cellcolor[HTML]{F5F5F5} & \multicolumn{8}{c}{\cellcolor[HTML]{F5F5F5}} \\ Qwen2.5-VL-3B-Instruct[1fps] & 41.7 & 47.7 & 33.4 & 34.8 & 39.2 & 42.6 & 42.3 & 41.2 & 43.9\\ Qwen2.5-VL-7B-Instruct[1fps] & 45.4 & 40.2 & 53.4 & 38.0 & 40.8 & 47.8 & 46.3 & 44.1 & 56.1\\ \multicolumn{1}{l|}{\cellcolor[HTML]{FFE4B5}\textit{\textbf{Proposed Embodied-R}}} & \cellcolor[HTML]{FFE4B5} & \multicolumn{8}{c}{\cellcolor[HTML]{FFE4B5}} \\
VLM-72B + LLM-3B [$\leq$32f] & 51.1 & 55.1 & 59.9 & 39.7 & 47.6 & 50.0 & 44.3 & 36.8 & 72.0\\
\hline\end{tabular}\end{minipage} \hspace{0.7cm} 
		\begin{minipage}[t]{0.38\textwidth} 
\vspace{-6.5cm} \centering \includegraphics[width=\linewidth]{fig/rader.png} 
\vspace{-0.1cm} 


\caption{Ablation of Key-Frame Extractor} \label{tab:ablation_kfe} \vspace{-0.3cm} 
\small
\setlength{\tabcolsep}{3pt} \begin{tabular}{ccccc}\toprule & \makecell{Avg. \\ Frame} & Acc. & \makecell{Training \\ Time} & \makecell{Inference \\ Time} \\ \hline w/o & 32 & 49.5&127.87 h & 243.68 s\\ w & $20.7^{\color{red}\downarrow 11.3}$ & $51.1^{\color{green!70!black}\uparrow 1.6}$ &$111.70 h^{\color{red}\downarrow 16.17}$ & $157.55 s^{\color{red}\downarrow 86.13}$ \\ \bottomrule\end{tabular} \vspace{0.47cm} 

\caption{Ablation of Collaboration.} \label{tab:ablation_collaboration} \vspace{-0.17cm} \setlength{\tabcolsep}{1.3pt} \begin{tabular}{cccccccccc}
\hline
     & Avg. & LP   & C    & PE   & AG    & RDist& RDir & RP    & AO\\ \hline
w/o  & 34.8 & 31.8 & 45.7 & 28.3 & 28.1 & 41.0 & 29.7 & 37.5  & 46.0  \\
w    & 51.1 & 55.1 & 59.9 & 39.7 & 47.6 & 50.0 & 44.3 & 36.8 & 72.0\\
$ \bigtriangleup $    & 
\color{green!70!black}+16.3 &  
\color{green!70!black}+23.3 & 
\color{green!70!black}+14.2 & 
\color{green!70!black}+11.4 & 
\color{green!70!black}+19.5 & 
\color{green!70!black}**** & 
\color{green!70!black}+14.6 & 
\color{red}-0.7 & 
\color{green!70!black}+26.0
\\ \bottomrule
\end{tabular}\end{minipage}
	\end{tabular}
\end{table*}

We first provide the details of the experimental setup and then demonstrate the
following: quantitative results, qualitative results, and ablation studies.
These correspond to addressing the following three research questions (RQs):
\begin{itemize}[leftmargin=*]
	\item \textbf{RQ1: How does Embodied-R perform compared to existing video-LLMs?}

	\item \textbf{RQ2: Has Embodied-R learned slow-thinking?}

	\item \textbf{RQ3: What are the contributions of each module?}
\end{itemize}

\begin{figure*}[t!]
	\centering
	\includegraphics[width=0.95\linewidth]{fig/case.png}
	\vspace{-10pt}
	\caption{Case Analysis: Embodied-R has initially developed the ability for slow-thinking: it can think before answering, effectively distinguish spatial relationships, provide structured and organized responses, and integrate information across multiple frames for embodied scene analysis.}
	\label{Fig:case}
	\vspace{-5pt}
\end{figure*}

\begin{figure}[t]
	\centering
	\includegraphics[width=3in]{fig/ablation_RL.pdf}
	\vspace{-5pt}
	\caption{Ablation of RL training and comparison to other language models.}
	\label{Fig:ablation_rl}
	\vspace{-15pt}
\end{figure}

\subsection{Experimental Setup}
\subsubsection{\textbf{Data Preparation}} \label{sec:data_prepare}

We primarily focus on spatial reasoning problems during motion within three-dimensional
physical space to evaluate the effectiveness of our method. For this purpose, we
selected two embodied video datasets as the main training and testing sets:
VSI-Bench~\cite{yang2024thinking},
which contains indoor first-person navigation data.
, and UrbanVideo-Bench~\cite{zhao2025urbanvideobench}, which consists of outdoor embodied
data captured by drones navigating through aerial spaces These datasets provide
diversity in scenarios by incorporating both outdoor and indoor video data. Based
on the content of the tasks, we specifically selected four distinct types of
tasks from each dataset, characterized by long spatial reasoning chains and low accuracy.
These tasks are formulated as multiple-choice question-answering problems, ensuring
determinism in answers to facilitate RL training and allowing direct calculation
of accuracy to evaluate performance. Across eight task categories, the dataset covers
multiple levels of spatial reasoning, comprising a total of 5,415 QA pairs and 1,492
videos. Additionally, we include two out-of-distribution dataset, EgoSchema~\cite{mangalam2023egoschema}
and Egocentric task in MVBench~\cite{li2024mvbench}. EgoSchema is designed for
task-level reasoning from a first-person perspective, with 500 QA pairs and 500 videos
available in its fully open-source portion. MVBench encompasses the embodied
task of egocentric navigation, comprising 200 QA pairs and 200 corresponding videos.
These datasets serves to evaluate the generalization capability of the trained
model.

To ensure comprehensive evaluation, we conducted five repeated experiments. The
dataset was randomly divided into five equal parts and 5-fold cross-validation
is adopted. The final testing results are averaged across the five experiments.
Furthermore, we address the issue of potential semantic bias in the datasets.
For instance, in action generation tasks, forward movement may inherently have a
higher correctness rate than adjusting the gimbal angle, which is a characteristic
of the task itself. To prevent the testing performance from being influenced by
the model learning textual distribution rather than truly understanding the
spatial information in video, we implement an additional filtering step for the testing
set. Specifically, we train a LLM through supervised fine-tuning using only the textual
QA pairs from the training set, without video inputs. If a question in the testing
set can be correctly answered by the fine-tuned LLM but not by the original LLM,
it indicates semantic bias in that QA pair. These biased QA pairs are excluded from
the testing set as they fail to accurately assess the spatial reasoning capabilities
of models.

\subsubsection{\textbf{Implementation Details}}
We use Qwen2.5-3B-Instruct~\cite{yang2024qwen2} as the small-scale LM and Qwen2.5-VL-72B-Instruct~\cite{bai2025qwen2} as large-scale
VLM. Both training and inference processes were conducted using 8 NVIDIA A800-SXM4-40GB
GPUs, with each RL training requiring approximately 90 GPU hours. Other key
hyperparameters for training are as follows: learning rate: 5e-7, temperature:
1.0, train batch size: 32, rollout size: 8, KL coefficient: 0.001, maximum response
length: 2048, input length: 6144. When conducting inference on the test set, the
temperature is set to 0.5.

\subsubsection{\textbf{Three-Stage Training Schedule}}
As for the RL training on the LM, we design a three-stage training schedule to
achieve a smooth improvement in training performance. The primary distinction between
stages lies in the different weight ratios assigned to three types of rewards.

\begin{itemize}[leftmargin=*]
	\item \textbf{Stage 1:} In epochs 1 and 2, the goal is to guide the model to
		follow the "<think> </think> <answer> </answer>" output format. At this
		stage, the weights are set as $\omega_{1}:\omega_{2}:\omega_{3}= 7:3:0$.
		Correct format rewards also assist in locating the answer and reduce
		misjudgment in accuracy. During this phase, the format reward rapidly
		converges to 1.

	\item \textbf{Stage 2:} In epochs 3 and 4, the focus shifts to improving the
		accuracy of the model's responses, guiding the model to produce correct
		reasoning answers. The weights are set as $\omega_{1}:\omega_{2}:\omega_{3}
		= 3:7:0$.

	\item \textbf{Stage 3:} In subsequent 5-12 epochs, the aim is to enhance accuracy
		while simultaneously improving the quality of the "thinking" process,
		ensuring logical consistency between thinking and the answer. The weights
		are set as $\omega_{1}:\omega_{2}:\omega_{3}= 1:7:2$.
\end{itemize}

\subsection{How Does Embodied-R Perform Compared to Existing Video-LLMs?}

To evaluate the effectiveness of the proposed method, in addition to the random baseline, we introduced four categories comprising 17 multimodal large language models capable of processing video inputs:

\begin{itemize}[leftmargin=*]
	\item \textbf{Proprietary Models:} Cost-effective multimodal models with over 100B parameters, including Qwen-VL-Max~\cite{Qwen_Website}, GPT-4o~\cite{OpenAI_API}, Gemini-1.5-Flash~\cite{team2023gemini}, and Gemini-1.5-Pro~\cite{team2023gemini}.
    \item \textbf{SOTA Reasoning Models:} State-of-the-art reasoning models with the highest performance but significant computational cost, including OpenAI-o1~\cite{openai2024b} and Gemini-2.5-Pro~\cite{Gemini_API}.
    \item \textbf{Open-Source Models:} Popular open-source multimodal models, including LLaVA-NeXT-Video-7B-hf~\cite{lin2023video}, Phi-3.5-vision-instruct~\cite{abdin2024phi}, the Internvl2 series~\cite{chen2024internvl}, and the Qwen-VL series~\cite{bai2025qwen2}.
    \item \textbf{Supervised Fine-Tuning (SFT):} Considering the scarcity of embodied video tasks, the aforementioned models may lack exposure to relevant data. Therefore, Qwen2.5-VL-3B-Instruct~\cite{bai2025qwen2} and Qwen2.5-VL-7B-Instruct~\cite{bai2025qwen2} are fine-tuned for these tasks.
\end{itemize}

The results presented in Table~\ref{tab:acc} lead to the following conclusions:

\begin{itemize}[leftmargin=*]
    \item After undergoing RL training on embodied reasoning tasks, our model significantly outperformed proprietary models as well as OpenAI-o1 and Gemini-2.5-Pro by over \textbf{10\%}. Moreover, it consistently demonstrated leading performance across various tasks. These results highlight the considerable \textbf{difficulty of embodied reasoning tasks} and indicate that current reasoning models lack generalization capability for such spatial reasoning challenges. On the other hand, the findings confirm that \textbf{collaborative framework with RL can effectively enhance model reasoning performance in specific domains}, especially for tasks that remain poorly solved.
    
    \item For embodied video reasoning, a highly coupled perception-reasoning problem, the VLM model Qwen2.5-VL-72B-Instruct achieved an accuracy of only 34.9\% through direct inference. In contrast, incorporating a small-scale LM model improved accuracy to 51.1\%. \textbf{Given limited computational resources for training, the collaborative framework proposed in this study provides an effective solution for balancing model size with hardware constraints}.

    \item Under similar computational resource limitations, direct fine-tuning is restricted to models with a size of 7B or smaller. However, the perceptual capacity of small-scale VL models imposes a low upper bound on accuracy compared to Embodied-R. Additionally, fine-tuned models lack the capability for slow-thinking.
\end{itemize}





\subsection{Has Embodied-R Learned Slow-Thinking?}

Beyond the quantitative results, we aim to explore whether spatial reasoning capabilities in the output of Embodied-R are improved. As illustrated in Figure~\ref{Fig:case}, after RL training, Embodied-R demonstrates the following human-like reasoning ways:

\begin{itemize}[leftmargin=*]
    \item \textbf{Spatial Relationship Reasoning}: Accurately inferring the relative spatial relationship between itself and the surrounding environment.  
    \item \textbf{Systematic Analysis}: Breaking down problems into components, presenting answers with a "part-to-whole" structure, and maintaining clear logical organization.

    \item \textbf{Contextual Integration}: Integrating semantic information across different frames to perform comprehensive analysis.

    \item \textbf{Think-Answer Format}: Strictly adhering to a structured process of reasoning before outputting the final answer. 
\end{itemize}
In summary, Embodied-R demonstrates a certain degree of slow-thinking capability in embodied spatial reasoning.


\begin{figure*}[t!]
	\centering
	\includegraphics[width=\linewidth]{fig/training_process_2.png}
	\vspace{-15pt}
	\caption{a-d. The GRPO training process (a: accuracy reward; b: format reward; c: ratio of logical consistency reward to accuracy reward; d: response length of validation set).
    e. Comparison of accuracy reward curves for RL training of equivalently sized LM and VLM models.
f. Model performance before and after integrating logical consistency reward.
g. Comparison of generalization performance between models trained with RL and SFT.}
	\label{Fig:further_exploration}
	\vspace{-10pt}
\end{figure*}


\subsection{Contributions of Each Module}
\subsubsection{\textbf{Ablation of Key-Frame Extractor}}

The role of Key-Frame Extractor is to reduce inference time and training time by retaining essential frames and removing redundant ones while maintaining perceptual quality. As shown in Table~\ref{tab:ablation_kfe}, while improving accuracy, training time is significantly reduced by 12.6\%, and single inference time is reduced by approximately one-third.


\subsubsection{\textbf{Ablation of Collaboration}}


The collaborative framework enables improved reasoning capabilities under limited computational resources for training. With training-free large-scale pre-trained VLMs, it only requires training small-scale LM models to achieve enhanced reasoning performance. As shown in Table~\ref{tab:ablation_collaboration}, with identical key-frame inputs and using the same VLM, Qwen2.5-VL-72B-Instruct, the overall accuracy of collaborative inference is 1.5 times higher than that of the standalone VLM.



\subsubsection{\textbf{Ablation of RL Training}}


RL is central to the LM training in this paper. Without RL training, directly applying the original LM-3B model for reasoning leads to poor performance, as the LM has limited exposure to embodied spatial reasoning data during pretraining. After RL training, the LM achieves significant improvements, with a 27.9\% increase on the UrbanVideo-Bench and a 20.6\% increase on the VSI-Bench benchmarks. 

Given that VLM has already transformed visual inputs into textual representations, we introduced 4 text-based reasoning models (o3-mini~\cite{openai2025o3mini}, Deepseek-R1~\cite{guo2025deepseek}, Qwen-Max~\cite{Qwen_Website}, Qwen2.5-7B-Instruct~\cite{bai2025qwen2}) as baselines to further assess the importance of reasoning capability in the embodied spatial task. The results demonstrate a clear positive correlation between the reasoning ability of the model and its accuracy.
The strong performance of Embodied-R may not only stem from its familiarity with the data distribution but also from its synergy with the representations provided by the VLM. Following training, the small-scale LM becomes more attuned to the VLM-generated representations, which translates into enhanced performance on embodied reasoning tasks.



