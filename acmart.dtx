% \iffalse
%
% Copyright 2016-2025, Association for Computing Machinery
% This work may be distributed and/or modified under the
% conditions of the LaTeX Project Public License, either
% version 1.3 of this license or (at your option) any
% later version.
% The latest version of the license is in
%    http://www.latex-project.org/lppl.txt
% and version 1.3 or later is part of all distributions of
% LaTeX version 2005/12/01 or later.
%
% This work has the LPPL maintenance status `maintained'.
%
% The Current Maintainer of this work is <PERSON>,
% <<EMAIL>>
%
% This work consists of the file acmart.dtx, the derived file
% acmart.cls, the files ACM-Reference-Format.bst, and templates
% sample-acmlarge.tex, sample-acmsmall.tex, sample-acmtog.tex,
% samplebody-conf.tex, samplebody-journals.tex, sample-manuscript.tex,
% sample-sigconf-authordraft.tex, sample-sigconf.tex,
% sample-sigplan.tex
%
% \fi
%
%
%% \CharacterTable
%%  {Upper-case    \A\B\C\D\E\F\G\H\I\J\K\L\M\N\O\P\Q\R\S\T\U\V\W\X\Y\Z
%%   Lower-case    \a\b\c\d\e\f\g\h\i\j\k\l\m\n\o\p\q\r\s\t\u\v\w\x\y\z
%%   Digits        \0\1\2\3\4\5\6\7\8\9
%%   Exclamation   \!     Double quote  \"     Hash (number) \#
%%   Dollar        \$     Percent       \%     Ampersand     \&
%%   Acute accent  \'     Left paren    \(     Right paren   \)
%%   Asterisk      \*     Plus          \+     Comma         \,
%%   Minus         \-     Point         \.     Solidus       \/
%%   Colon         \:     Semicolon     \;     Less than     \<
%%   Equals        \=     Greater than  \>     Question mark \?
%%   Commercial at \@     Left bracket  \[     Backslash     \\
%%   Right bracket \]     Circumflex    \^     Underscore    \_
%%   Grave accent  \`     Left brace    \{     Vertical bar  \|
%%   Right brace   \}     Tilde         \~}
%
%
% \MakeShortVerb{|}
% \def\guide{acmguide}
% \iffalse
% From
% http://tex.stackexchange.com/questions/117892/can-i-convert-a-string-to-catcode-11 by egreg
% \fi
% \begingroup
%  \everyeof{\noexpand}
%  \endlinechar=-1
%  \xdef\currentjob{\scantokens\expandafter{\jobname}}
% \endgroup
%
% \ifx\currentjob\guide\OnlyDescription\fi
% \GetFileInfo{acmart.dtx}
% \title{\LaTeX{} Class for the \emph{Association for Computing
% Machinery}\thanks{\copyright 2016--2025, Association for Computing Machinery}}
% \author{Boris Veytsman\thanks{%
% \href{mailto:<EMAIL>}{\texttt{<EMAIL>}},
% \href{mailto:<EMAIL>}{\texttt{<EMAIL>}}}}
% \date{\filedate, \fileversion}
% \maketitle
% \begin{abstract}
%   This package provides a class for typesetting publications of
%   the Association for Computing Machinery.
% \end{abstract}
% \tableofcontents
%
% \clearpage
%
%\section{Introduction}
%\label{sec:intro}
%
% The Association for Computing
% Machinery\footnote{\url{http://www.acm.org/}} is the world's largest
% educational and scientific computing society, which delivers
% resources that advance computing as a science and a
% profession.  It was one of the
% early adopters of \TeX\ for its typesetting.
%
% It provided several different classes for a number of journals and
% conference proceedings.  Unfortunately during the years since these
% classes were written, the code was patched many times, and
% supporting different versions of the classes became difficult.
%
% This new consolidated template package replaces all previous
% independent class files and packages and provides a single
% up-to-date LaTeX package with optional calls. The package uses only
% free \TeX\ packages and fonts included in \TeX Live, Mik\TeX\ and
% other popular \TeX\ distributions. The new ACM templates use a new
% font set (libertine) which will need to be installed on your machine
% before using the templates. Please download and install the
% libertine font set before writing your paper. Fonts used in the
% template cannot be substituted; margin adjustments are not allowed. 
%
%  I am grateful to
%  Michael D.~Adams,
%  Leif Andersen,
%  Lawrence Christopher Angrave,
%  Dirk Beyer,
%  Andrew Black,
%  Joachim Breitner,
%  Yegor Bugayenko,
%  Benjamin Byholm,
%  John Collins,
%  Roberto Di Cosmo,
%  Nils Anders Danielsson,
%  Michael Ekstrand,
%  Matthew Fluet,
%  Paolo G.~Giarrusso,
%  Ben Greenman,
%  Enrico Gregorio,
%  Jamie Davis,
%  Ulrike Fischer,
%  Jason Hemann,
%  Peter Kemp,
%  Luis Leiva,
%  Ben Liblit,
%  Rholais Lii,
%  LianTze Lim,
%  Kuldeep S. Meel,
%  Kai Mindermann,
%  Frank Mittelbach,
%  Serguei Mokhov,
%  Ross Moore,
%  John Owens,
%  Joel Nider,
%  Scott Pakin,
%  Tobias Pape,
%  Henning Pohl,
%  Philip Quinn,
%  Mathias Rav,
%  Andreas Reichinger,
%  Matteo Riondato,
%  Craig Rodkin,
%  Bernard Rous,
%  Feras Saad,
%  Kerry A. Seitz, Jr.,
%  M Senthilkumar,
%  David Shamma,
%  Gabriel Scherer,
%  Kartik Singhal,
%  Christoph Sommer,
%  Stephen Spencer,
%  Shin Hwei Tan,
%  Daniel Thomas,
%  Shari Trewin,
%  Zack Weinberg,
%  John Wickerson
%  and many others for their invaluable help.
%
% The development version of the package is available at
% \url{https://github.com/borisveytsman/acmart}.
%
%\section{User's guide}
%\label{sec:ug}
%
%
% This class uses many commands and customizaton options, so it might
% appear intimidating for a casual user.  Do not panic!  Many of these
% commands and options can be safely left with their default values
% or the values recommended by your conference or journal editors.  If
% you have problems or questions, do not hesitate to ask me directly
% or the community at \url{https://github.com/borisveytsman/acmart},
% \url{https://tex.stackexchange.com} or the closest \TeX\ Users
% Group.  The world-wide \TeX\ Users Group is at
% \url{https://tug.org/}; please consider joining us if you use \TeX\
% regularly.
%
%\subsection{Installation}
%\label{sec:ug_install}
%
% Most probably, you already have this package installed in your
% favorite \TeX\ distribution;  if not, you may want to upgrade.  You
% may need to upgrade it anyway since this package uses a number of
% relatively recent packages, especially the ones related to fonts.
%
% The latest released version of this package can be found on CTAN:
% \url{https://www.ctan.org/pkg/acmart}.   The development version can
% be found on GitHub: \url{https://github.com/borisveytsman/acmart}.
% At this address you can file a bug report---or even contribute your
% own enhancement by making a pull request.
%
% Please note that the version on Github is a development (or
% experimental) version: please download it for testing new features.
% The production version is the one on CTAN and ACM sites.
%
% Most users should not attempt to install this package themselves
% but should rather rely on their \TeX\ distributions to provide it.  If you
% decide to install the package yourself, follow the standard rules:
% \begin{enumerate}
% \item Run |latex acmart.ins|.  This will produce the file
% |acmart.cls|
% \item Put the files |acmart.cls|, |acm-jdslogo.png|,
%   and |ACM-Reference-Format.bst|
%   in places where \LaTeX{} can find them (see \cite{TeXFAQ} or
%   the documentation for your \TeX{} system).\label{item:install}
% \item Update the database of file names.  Again, see \cite{TeXFAQ}
% or the documentation for your \TeX{} system for the system-specific
% details.\label{item:update}
% \item The file |acmart.pdf| provides the documentation for the
% package.  (This is probably the file you are reading now.)
% \end{enumerate}
% As an alternative to items~\ref{item:install} and~\ref{item:update}
% you can just put the files in the working directory where your
% |.tex| file is.
%
%
% This class uses a number of other packages.  They are included in all
% major \TeX\ distributions (\TeX Live, Mac\TeX, Mik\TeX) of 2015 and
% later, so you probably have them installed.  Just in case here is
% the list of these packages:
% \begin{itemize}
% \item \textsl{amsart}, \url{https://ctan.org/pkg/amsart},
% \item \textsl{babel}, \url{https://ctan.org/pkg/babel},
% \item \textsl{balance}, \url{https://ctan.org/pkg/balance},
% \item \textsl{booktabs}, \url{https://ctan.org/pkg/booktabs},
% \item \textsl{caption}, \url{https://ctan.org/pkg/caption},
% \item \textsl{cmap}, \url{https://ctan.org/pkg/cmap},
% \item \textsl{comment}, \url{https://ctan.org/pkg/comment},
% \item \textsl{draftwatermark}, \url{https://ctan.org/pkg/draftwatermark},
% \item \textsl{environ}, \url{https://ctan.org/pkg/environ},
% \item \textsl{etoolbox}, \url{https://ctan.org/pkg/etoolbox},
% \item \textsl{fancyhdr}, \url{https://ctan.org/pkg/fancyhdr},
% \item \textsl{float}, \url{https://ctan.org/pkg/float},
% \item \textsl{fontenc}, \url{https://ctan.org/pkg/fontenc},
% \item \textsl{framed}, \url{https://ctan.org/pkg/framed},
% \item \textsl{geometry}, \url{https://ctan.org/pkg/geometry},
% \item \textsl{graphicx}, \url{https://ctan.org/pkg/graphicx},
% \item \textsl{hyperref}, \url{https://ctan.org/pkg/hyperref},
% \item \textsl{hyperxmp}, \url{https://ctan.org/pkg/hyperxmp},
% \item \textsl{iftex}, \url{https://ctan.org/pkg/iftex},
% \item \textsl{libertine}, \url{https://ctan.org/pkg/libertine},
% \item \textsl{manyfoot}, \url{https://ctan.org/pkg/manyfoot},
% \item \textsl{microtype}, \url{https://ctan.org/pkg/microtype},
% \item \textsl{natbib}, \url{https://ctan.org/pkg/natbib},
% \item \textsl{newtxmath}, \url{https://ctan.org/pkg/newtxmath},
% \item \textsl{pbalance}, \url{https://ctan.org/pkg/pbalance},
% \item \textsl{refcount}, \url{https://ctan.org/pkg/refcount},
% \item \textsl{setspace}, \url{https://ctan.org/pkg/setspace},
% \item \textsl{totpages}, \url{https://ctan.org/pkg/totpages},
% \item \textsl{unicode-math}, \url{https://ctan.org/pkg/unicode-math},
% \item \textsl{xcolor}, \url{https://ctan.org/pkg/xcolor},
% \item \textsl{xkeyval}, \url{https://ctan.org/pkg/xkeyval},
% \item \textsl{xstring}, \url{https://ctan.org/pkg/xstring},
% \item \textsl{zi4}, \url{https://ctan.org/pkg/zi4},
% \item \textsl{zref-savepos}, \url{https://ctan.org/pkg/zref-savepos},
% \item \textsl{zref-user}, \url{https://ctan.org/pkg/zref-user}.
% \end{itemize}
%
%
%\subsection{Invocation and options}
%\label{sec:invocation}
%
% To use this class, put in the preamble of your document
% \begin{quote}
%   \cs{documentclass}\oarg{options}|{acmart}|
% \end{quote}
% There are several options corresponding to the type of the document and
% its general appearance.  They are described below.  Generally
% speaking, the options have |key=value| forms, for example,
% \begin{verbatim}
% \documentclass[format=acmsmall, screen=true, review=false]{acmart}
% \end{verbatim}
%
%
% The option |format| describes the format of the output.  There are
% several possible values for this option, for example,
% \begin{verbatim}
%   \documentclass[format=acmtog]{acmart}
% \end{verbatim}
% Actually the words |format=| can be omitted, e.g.,
% \begin{verbatim}
%   \documentclass[acmtog, review=false]{acmart}
% \end{verbatim}
% The possible formats are listed in Table~\ref{tab:opts_format}.
% Note that formats starting with |acm| are intended for journals,
% transactions, and course materials, while formats starting with
% |sig| are intended for proceedings published as books.
%
% Sometimes conference proceedings are published as a special issue
% (or issues) of an ACM journal.  In this case, you should use the
% journal format.  The templates |sample-acmsmall-conf.tex| and
% |sample-acmtog-conf.tex| show how to enter conference information.
% Note that you need to comment out |\acmJournal{...}| line for such
% papers to get the conference information in the footers and headers.
%
% \begin{table}
%   \centering
%   \caption{The possible values for the \texttt{format} option}
%   \label{tab:opts_format}
%   \begin{tabularx}{\textwidth}{>{\ttfamily}lX}
%     \toprule
%     \normalfont Value & Meaning\\
%     \midrule
%   manuscript & A manuscript. This is the default. \\
%   acmsmall & Small single-column format.  Used for ACMJCSS, CIE, CSUR,
%            FAC, GAMES, JACM, JATS,  JDIQ, JDS, JEA, JERIC,
%            JETC, JRC, PACMCGIT, PACMHCI, PACMMOD, PACMNET,
%            PACMPL, PACMSE, POMACS, TAAS, TACCESS, TACO,
%            TALG, TALLIP (formerly TALIP), TCPS, TDS,
%            TEAC, TECS, TELO, THRI, TIIS, TIOT, TISSEC, TIST, TKDD, TMIS,
%            TOCE, TOCHI, TOCL,
%            TOCS, TOCT, TODAES, TODS, TOIS, TOIT, TOMACS, TOMM (formerly
%            TOMCCAP), TOMPECS, TOMS, TOPC, TOPLAS, TOPML, TOPS, TORS
%            TOS, TOSEM, TOSN, TQC, TRETS,
%            TSAS, TSC, TSLP, and TWEB, including special issues. \\
%   acmlarge  & Large single-column format.  Used for DLT, DTRAP, HEALTH,
%           IMWUT, JOCCH, and TAP, including special issues. \\
%   acmtog   & Large double-column format.  Used for
%          TOG, including annual conference Technical Papers.\\
%   sigconf & Proceedings format for most ACM
%          conferences (with the exception of SIGPLAN) and all ICPS
%          volumes.\\
%   sigplan & Proceedings format for SIGPLAN conferences.\\
%   acmengage & ACM EngageCSEdu Course materials.\\
%   acmcp & ACM cover page. \\
%   \bottomrule
%   \end{tabularx}
% \end{table}
%
% Starting in 2020, ACM retired formats |sigchi| and |sigchi-a|.
% SIGCHI conferences now use |sigconf| format for their publications.
% If a file uses |sigchi| format, a warning is issued, and the format
% is automatically switched to |sigconf|.  Format |sigchi-a| can be
% used for non-ACM documents only (see Section~\ref{sec:sigchi-a}).
% The format |acmcp| is used for ACM cover pages discussed in
% Section~\ref{sec:ug_acmcp}.
%
%
% There are several Boolean options that can take |true| or |false|
% values.  They are listed in Table~\ref{tab:opts_bool}.  The words
% |=true| can be omitted when setting a Boolean option, so instead of
% |screen=true| one can write just |screen|, for example,
% \begin{verbatim}
% \documentclass[acmsmall, screen, review]{acmart}
% \end{verbatim}
% The option |review| is useful when combined with the |manuscript| format
% option.  It provides a version suitable for reviewers and
% copy editors.
%
% Two samples in the |samples| directory, |manuscript| and
% |acmsmall-submission|, show manuscripts formatted for submission to
% ACM.
%
% The default for the option |screen| depends on the publication.  At
% present it is |false| for all publications \emph{but} PACM, since
% PACM is now electronic-only.  Thus PACM titles~(see
% Table~\ref{tab:pubs}) set this option to |true|.  In the future this
% option may involve additional features suitable for on-screen
% versions of articles.
%
% The option |natbib| is used when the corresponding
% \BibTeX\ style is based on |natbib|.  In most cases you do not need
% to set it.  See
% Section~\ref{sec:ug_bibliography}.
%
% The option |anonymous| is used
% for anonymous review processes and causes all author information to be
% obscured.
%
% The option |timestamp| is used to include a time stamp in the
% footer of each page.  When preparing a document, this can help avoid
% confusing different revisions.  The footer also includes the page range of
% the document.  This helps detect missing pages in hard copies.
%
% The option |authordraft| is intended for author's drafts that are not
% intended for distribution.  It typesets a copyright block to give the
% author an idea of its size and the overall size of the paper but
% overprints it with the phrase ``Unpublished working draft. Not for
% distribution.'', which is also used as a watermark.  This option sets
% |timestamp| and |review| to |true|, but these can be
% overriden by setting these options to |false| \emph{after}
% setting |authordraft| to |true|.
%
% The option |balance| determines whether the last page in the two
% column mode has balanced columns.  By default it is |true|; however,
% it may lead to problems for some documents.  When there are many
% figures near the end of the document, the attempts to balance
% columns may lead to the loss of the figures.  Set this option to |false| if
% you encounter problems.   An alternative is the
% (experimental) option |pbalance|, which uses the new package
% |pbalance|.  You may want to try |pbalance=true| to see if you get
% better results.  
%
% The option |urlbreakonhyphens| determines whether URLs can be split
% between lines after hyphens.  By default it is true.  Set it to
% |false| to disallow these breaks.
%
% \begin{table}
%   \centering
%   \caption{Boolean options}
%   \label{tab:opts_bool}
%   \begin{tabularx}{\textwidth}{>{\ttfamily}l>{\ttfamily}lX}
%     \toprule
%     \normalfont Option & \normalfont Default & Meaning\\
%     \midrule
%     review & false & A review version: lines are numbered and
%     hyperlinks are colored\\
%     screen & {\rmfamily see text} & A screen version:
%     hyperlinks are colored\\
%     natbib & true & Whether to use the |natbib| package (see
%     Section~\ref{sec:ug_bibliography})\\
%     anonymous & false & Whether to make author(s) anonymous\\
%     authorversion & false & Whether to generate a special
%     version for the authors' personal use or posting (see
%     Section~\ref{sec:ug_topmatter})\\
%     nonacm & false & Use the class typesetting options for
%     a non-ACM document, which will not include the conference/journal
%     header and footers.  Currenly such documents allow only a
%     Creative Commons license.\\
%     timestamp & false & Whether to put a time stamp in the
%     footer of each page\\
%     authordraft & false & Whether author's-draft mode is enabled\\
%     acmthm & true & Whether to define theorem-like environments, see
%                       Section~\ref{sec:ug_theorems}\\
%     balance & true & Whether to balance the last page in two column
%                        mode\\
%     pbalance & false & Whether to balance the last page in two column
%                        mode using pbalance package\\
%     urlbreakonhyphens & true & Whether to break urls on hyphens\\
%     \bottomrule
%   \end{tabularx}
% \end{table}
%
% The option |language| is used to define the languages for the
% multi-language papers.  It is discussed in
% Section~\ref{sec:ug_i13n}. 
%
%\subsection{Top matter}
%\label{sec:ug_topmatter}
%
% A number of commands set up \emph{top matter} or (in
% computer science jargon) \emph{metadata} for an article.  They
% establish the publication name, article title, authors, DOI and
% other data.  Some of these commands, like \cs{title} and \cs{author},
% should be put by the authors.  Others, like \cs{acmVolume} and
% \cs{acmDOI}---by the editors.  Below we describe these commands and
% mention who should issue them.  These macros should be used
% \emph{before} the \cs{maketitle} command.  Note that in previous
% versions of ACM classes some of these commands should be used before
% \cs{maketitle}, and some after it. Now they all must be used before
% \cs{maketitle}.
%
%
% This class internally loads the |amsart| class, so many top-matter
% commands are inherited from |amsart|~\cite{Downes04:amsart}.
%
% \DescribeMacro{\acmJournal}%
% The macro \cs{acmJournal}\marg{shortName} sets the name of the
% journal or transaction for journals and transactions.  The argument
% is the short name of the publication \emph{in uppercase}, for
% example,
% \begin{verbatim}
% \acmJournal{TOMS}
% \end{verbatim}
% The currently recognized journals are listed in
% Table~\ref{tab:pubs}.  Note that conference proceedings published in
% \emph{book} form do not set this macro.
%
%
%
% \DescribeMacro{\acmConference}%
% The macro
% \cs{acmConference}\oarg{short name}\marg{name}\marg{date}\marg{venue} is
% used for conference proceedings published in the book form.  The
% arguments are the following:
% \begin{description}
% \item[short name:] the abbreviated name of the conference (optional).
% \item[name:] the name of the conference.
% \item[date:] the date(s) of the conference.
% \item[venue:] the place of the conference.
% \end{description}
% Examples:
% \begin{verbatim}
% \acmConference[TD'15]{Technical Data Conference}{November
% 12--16}{Dallas, TX, USA}
% \acmConference{SA'15 Art Papers}{November 02--06, 2015}{Kobe, Japan}
% \end{verbatim}
%
% \DescribeMacro{\acmBooktitle}%
% By default we assume that conference proceedings are published
% in the book named \emph{Proceedings of \textsc{CONFERENCE}}, where
% \textsc{CONFERENCE} is the name of the conference inferred from the
% command \cs{acmConference} above.  However, sometimes the book title
% is different.  The command \cs{acmBooktitle} can be used to set this
% title, for example,
% \begin{verbatim}
% \acmBooktitle{Companion to the first International Conference on the
% Art, Science and Engineering of Programming (Programming '17)}
% \end{verbatim}
%
%
% An ACM paper should have either \cs{acmJournal} or
% \cs{acmConference} command.  If it has both (or more) commands, the
% last one takes precedence.  Note that if you have the command
% \cs{acmConference} in a journal format like |acmsmall|, the class
% will use conference format for bibstrip and reference citation
% formatting.  In the samples directory there is a file
% |sample-acmsmall-conf.tex| with the example of this usage.
%
% An ACM Engage material should \emph{not} use \cs{acmJournal} or
% \cs{acmConference} command.  It may use \cs{acmBooktitle} to
% override the default \emph{ACM EngageCSEdu}.  It should use \cs{acmYear}
% to set the date of the material.
%
%
%
% \DescribeMacro{\editor}%
% In most cases, conference proceedings are edited.  You can use the
% command \cs{editor}\marg{editor} to set the editor of the volume.
% This command can be repeated, for example,
% \begin{verbatim}
% \editor{Jennifer B. Sartor}
% \editor{Theo D'Hondt}
% \editor{Wolfgang De Meuter}
% \end{verbatim}
%
%
% \DescribeMacro{\title}%
% The command |\title|, as in the |amsart| class, has two arguments:  one
% optional, and one mandatory:
% \begin{flushleft}
%   |\title[|\meta{ShortTitle}|]{|\meta{FullTitle}|}|
% \end{flushleft}
% The mandatory argument is the full title of the article.  The
% optional argument, if present, defines the shorter version of the
% title for running heads.  If the optional argument is absent, the
% full title is used instead.
%
% It is expected that this command is inserted by the author of the
% manuscript.
%
% \DescribeMacro{\subtitle}%
% Besides title, ACM classes allow a subtitle, set with the
% \cs{subtitle}\marg{subtitle} macro.
%
% The commands for specifying authors are highly structured.
% The reason is they serve double duty:  the authors' information is
% typeset in the manuscript \emph{and} is used by the metadata
% extraction tools for indexing and cataloguing.  Therefore it is very
% important to follow the guidelines exactly.
%
% \DescribeMacro{\author}%
% \DescribeMacro{\orcid}
% \DescribeMacro{\affiliation}%
% \DescribeMacro{\email}%
% The basic commands are \cs{author}, \cs{orcid} (for the researchers
% registered with ORCID, \url{http://www.orcid.org/}), \cs{affiliation} and
% \cs{email}.  In the simplest case, you enter them in this order:
% \begin{verbatim}
% \author{...}
% \orcid{...}
% \affiliation{...}
% \email{...}
% \end{verbatim}
% Do \emph{not} use the \LaTeX\ \cs{and} macro or commas, or \verb|\\|
% between the authors! Each author deserves his or
% her own \cs{author} command.  An attempt to list several authors or
% their e-mails in one command leads to a warning or an error.  This
% is not a bug, but the expected behavior.
%
% Note that some formats do not typeset e-mails or ORCID identifiers.
% Do not worry: the metadata tools will get them.
%
% ACM strongly encourages that you include ORCIDs for all authors
% before compiling or submitting for review and/or production
% processing.
%
% If you do not have an ORCID, you may get one for free by
% registering at \url{http://www.orcid.org/}.  
%
%
% Sometimes an author has several affiliations.  In this case, the
% \cs{affiliation} command should be repeated:
% \begin{verbatim}
% \author{...}
% \orcid{...}
% \affiliation{...}
% \affiliation{...}
% \email{...}
% \end{verbatim}
% Similarly you can repeat the \cs{email} command.
%
% You may have several authors with the same affiliation, different
% affiliations, or overlapping affiliations (author~$A_1$ is affiliated
% with institutions $I_1$ and $I_2$, while author $A_2$ is affiliated
% with $I_2$ only, author $A_3$ is affiliated with
% $I_1$ and $I_3$, etc.).  The recommended solution is to put the
% \cs{affiliation} commands after each author, possibly repeating them:
% \begin{verbatim}
% \author{...}
% \orcid{...}
% \affiliation{...}
% \affiliation{...}
% \email{...}
% \author{...}
% \orcid{...}
% \affiliation{...}
% \email{...}
% \author{...}
% \orcid{...}
% \affiliation{...}
% \affiliation{...}
% \email{...}
% \end{verbatim}
%  In some cases, when several authors share the same affiliation, you can
%  try to save space using the format
% \begin{verbatim}
% \author{...}
% \email{...}
% \author{...}
% \email{...}
% \affiliation{...}
% \end{verbatim}
%  However, this format is not generally recommended.
%
% \DescribeMacro{\additionalaffiliation}%
% In some cases, too many affiliations can take too much space.  The
% command \cs{additionalaffiliation}\marg{affiliation} creates a
% footnote after an author's name with the words ``Also with
% \marg{affiliation}''.  You should use this command only as a last
% resort.  An example of usage is:
% \begin{verbatim}
% \author{G. Tobin}
% \author{Ben Trovato}
% \additionalaffiliation{%
%   \institution{The Th{\o}rv{\"a}ld Group}
%   \city{Hekla}
%   \country{Iceland}}
% \affiliation{%
%   \institution{Institute for Clarity in Documentation}
%   \city{Dublin}
%   \state{Ohio}}
% \end{verbatim}
% Here Trovato and Tobin share their affiliation with the Institute
% for Clarity in Documentation, but only Ben Trovato is affiliated
% with The Th{\o}rv{\"a}ld Group.
%
%
% \DescribeMacro{\position}%
% \DescribeMacro{\institution}%
% \DescribeMacro{\department}%
% \DescribeMacro{\city}%
% \DescribeMacro{\state}%
% \DescribeMacro{\country}%
% The \cs{affiliation} and \cs{additionalaffiliation} commands are
% further structured to interact with the metadata extraction tools.
% Inside these commands you should use the \cs{position},
% \cs{institution}, \cs{department}, \cs{city}, 
% \cs{state}, and \cs{country} macros to indicate the
% corresponding parts of the affiliation.  Note that in some cases
% (for example, journals) these parts are not printed in the resulting
% copy, but they \emph{are} necessary since they are used by the XML
% metadata extraction programs.  Do \emph{not} put commas or |\\|
% between the elements of \cs{affiliation}.  They will be provided
% automatically.
%
% The fields \cs{institution}, \cs{city} and \cs{country} are
% mandatory.  If they are not provided, an error or a warning  is
% issued. Currently the absence of \cs{country} produces an error;
% ACM may change this in the future.
%
% Starting 2024, ACM no longer collects or print postal addresses of
% the authors.  Thus the commands \cs{streetaddress} and
% \cs{postcode}, present in the previous versions of the class, now
% produce a warining.
%
%
% An example of the author block:
% \begin{verbatim}
% \author{A. U. Thor}
% \orcid{1234-4564-1234-4565}
% \affiliation{%
%   \institution{University of New South Wales}
%   \department{School of Biomedical Engineering}
%   \city{Sidney}
%   \state{NSW}
%   \country{Australia}}
% \email{<EMAIL>}
% \author{A. N. Other}
% \affiliation{%
%   \institution{University of New South Wales}
%   \city{Sidney}
%   \state{NSW}
%   \country{Australia}}
% \author{C. O. Respondent}
% \orcid{1234-4565-4564-1234}
% \affiliation{%
%   \institution{University of Pennsylvania}
%   \city{Philadelphia}
%   \state{PA}
%   \country{USA}}
% \affiliation{%
%   \institution{University of New South Wales}
%   \city{Sidney}
%   \state{NSW}
%   \country{Australia}}
% \end{verbatim}
%
% Note that the old ACM conference formats did not allow more than six
% authors and required some effort from authors to achieve
% alignment.  The new format is much better in this.
%
%  Sometimes an author works in several departments within the same
%  insitution.  There could be two situations: the departments are
%  independent, or one department is within another.  In the first
%  case, just repeat the command \cs{department} several times.  To
%  handle the second case the command has an optional numerical
%  parameter.  The departments with higher numbers are higher in the
%  organizational chart.  Compare
% \begin{verbatim}
% \affiliation{%
%   \department[0]{Department of Lunar Studies} % 0 is the default
%   \department[1]{John Doe Institute} % higher than 0
%   \institution{University of San Serriffe}
%   \country{San Serriffe}}
% \end{verbatim}
%  and
% \begin{verbatim}
% \affiliation{%
%   \department{Department of Lunar Studies} % Not in the John Doe Institute!
%   \department{John Doe Institute}
%   \institution{University of San Serriffe}
%   \country{San Serriffe}}
% \end{verbatim}
%
%
%
% \DescribeMacro{\thanks}%
% Like |amsart| (and unlike standard \LaTeX{}), we allow
% |\thanks| only \emph{outside} of the commands |\title| and |\author|.
% This command is obsolete and should \emph{not} be used in most
% cases.  Do not list your acknowledgments or grant sponsors here.
% Put this information in the |acks| environment (see
% Section~\ref{sec:ug_acks}).
%
% \DescribeMacro{\authorsaddresses}%
% In some formats, addresses are printed as a footnote on the first
% page.  By default \LaTeX\ typesets them itself using the information
% you give it.  However, you can override its choice using the
% commmand \cs{authorsaddresses}\marg{contact addresses}, for example,
% \begin{verbatim}
% \authorsaddresses{%
%  Authors' addresses: G.~Zhou, Computer Science Department, College of
%  William and Mary, 104 Jameson Rd, Williamsburg, PA 23185, US;
%  V.~B\'eranger, Inria Paris-Rocquencourt, Rocquencourt, France;
%  A.~Patel, Rajiv Gandhi University, Rono-Hills, Doimukh, Arunachal
%  Pradesh, India; H.~Chan, Tsinghua University, 30 Shuangqing Rd,
%  Haidian Qu, Beijing Shi, China; T.~Yan, Eaton Innovation Center,
%  Prague, Czech Republic; T.~He, C.~Huang, J.~A.~Stankovic University
%  of Virginia, School of Engineering Charlottesville, VA 22903, USA;
%  T. F. Abdelzaher, (Current address) NASA Ames Research Center,
%  Moffett Field, California 94035.}
% \end{verbatim}
% You can \emph{suppress} printing authors' addresses by setting them
% to an empty string:  |\authorsaddresses{}|.  Please note that
% authors' addresses are mandatory for journal articles.
%
% \DescribeMacro{\titlenote}%
% \DescribeMacro{\subtitlenote}%
% \DescribeMacro{\authornote}%
% While the command \cs{thanks} generates a note without a footnote
% mark, sometimes the authors might need notes more tightly connected
% to the title, subtitle or author.  The commands \cs{titlenote},
% \cs{subtitlenote} and \cs{authornote} that follow the corresponding
% commands (\cs{title}, \cs{subtitle} and \cs{author}) generate such
% notes.  For example,
% \begin{verbatim}
% \title{This is a title}
% \titlenote{This is a titlenote}
% \author{A. U. Thor}
% \authornote{This is an authornote}
% \end{verbatim}
%
% Please never use a \cs{footnote} inside an \cs{author} or \cs{title}
% command since this confuses the metadata extraction software.  (Actually
% these commands now produce errors.)
%
% \DescribeMacro{\authornotemark}%
% Sometimes one may need to have the same footnote connected to
% several authors.  The command \cs{authornotemark}\oarg{number} adds
% just the footnote mark, for example,
% \begin{verbatim}
% \author{A. U. Thor}
% \authornote{Both authors contributed equally to the paper}
% ...
% \author{A. N. Other}
% \authornotemark[1]
% \end{verbatim}
% The correct numbering of these marks is the responsibility of the
% user.
%
% \DescribeMacro{\acmVolume}%
% \DescribeMacro{\acmNumber}%
% \DescribeMacro{\acmArticle}%
% \DescribeMacro{\acmYear}%
% \DescribeMacro{\acmMonth}%
% The macros \cs{acmVolume}, \cs{acmNumber}, \cs{acmArticle},
% \cs{acmYear} and \cs{acmMonth} are inserted by the editor and set
% the journal volume, issue, article number, year and month
% corrspondingly.  The arguments of all these commands, including
% \cs{acmMonth}, is numerical.  For example,
% \begin{verbatim}
% \acmVolume{9}
% \acmNumber{4}
% \acmArticle{39}
% \acmYear{2010}
% \acmMonth{3}
% \end{verbatim}
% Note that \cs{acmArticle} is used not only for journals but also
% for some conference proceedings.
%
% \DescribeMacro{\acmArticleSeq}%
% The articles in the same issue of a journal have a \emph{sequence
% number}.  By default it is the same as the article number,
% but the command \cs{acmArticleSeq}\marg{n} can be used to change it:
% \begin{verbatim}
% \acmArticle{39}   % The sequence number will be 39 by default
% \acmArticleSeq{5} % We redefine it to 5
% \end{verbatim}
%
% \DescribeMacro{\acmSubmissionID}%
% If your paper got a Submission~ID from the Conference Management
% System, put it here:
% \begin{verbatim}
% \acmSubmissionID{123-A56-BU3}
% \end{verbatim}
%
%
%
% \DescribeMacro{\acmISBN}%
% Book-like volumes have ISBN numbers attached to them.  The macro
% \cs{acmISBN}\marg{ISBN} sets it.  Normally it is set by the
% typesetter, for example,
% \begin{verbatim}
% \acmISBN{978-1-4503-3916-2}
% \end{verbatim}
% Setting it to the empty string, as |\acmISBN{}|, suppresses printing the
% ISBN.
%
% \DescribeMacro{\acmDOI}%
% The macro \cs{acmDOI}\marg{DOI} sets the DOI of the article, for
% example,
% \begin{verbatim}
% \acmDOI{10.1145/9999997.9999999}
% \end{verbatim}
% It is normally set by the typesetter.  Setting it to the empty
% string, as |\acmDOI{}|, suppresses the DOI.
%
%
% \DescribeMacro{\acmBadge}%
% Some conference articles get special distinctions, for example, the
% artifact evaluation for PPoPP~2016
% (see~\url{http://ctuning.org/ae/ppopp2016.html}).  These articles
% display special badges supplied by the conference organizers.  This
% class provides command to add these badges:
% \cs{acmBadge}\oarg{url}\marg{graphics}.
% The arguments have the following meaning: \oarg{url}, if provided,
% sets the link to the badge authority in the screen version, while
% \marg{graphics} sets the graphics file with the badge image.  The
% file must be a cropped square, which is scaled to a standard size in
% the output.  For example, if the badge image is |ae-logo.pdf|, the
% command is
% \begin{verbatim}
% \acmBadge[http://ctuning.org/ae/ppopp2016.html]{ae-logo}
% \end{verbatim}
% The command can be repeated, if a paper has several badges.  
%
%
% \DescribeMacro{\startPage}%
% The macro \cs{startPage}\marg{page} sets the first page of the
% article in a journal or book.  It is used by the typesetter.
%
%
% \DescribeMacro{\terms}%
% \DescribeMacro{\keywords}%
% The command
% \cs{keywords}\marg{keyword, keyword,\ldots} sets keywords for the
% article.  They must be
% separated by commas, for example,
% \begin{verbatim}
% \keywords{wireless sensor networks, media access control,
% multi-channel, radio interference, time synchronization}
% \end{verbatim}
%
% \DescribeEnv{CCSXML}%
% \DescribeMacro{\ccsdesc}%
% ACM publications are classified according to the ACM Computing
% Classification Scheme (CCS).  CCS codes are used both in the typeset
% version of the publications \emph{and} in the metadata in various
% databases.  Therefore you need to provide both \TeX\ commands and XML
% metadata with the paper.
%
% The tool at \url{http://dl.acm.org/ccs.cfm} can be used to generate
% CCS codes.  After you select the topics, click on ``Generate CCS
% codes'' to get results like the following:
% \begin{verbatim}
% \begin{CCSXML}
% <ccs2012>
%  <concept>
%   <concept_id>10010520.10010553.10010562</concept_id>
%   <concept_desc>Computer systems organization~Embedded systems</concept_desc>
%   <concept_significance>500</concept_significance>
%  </concept>
%  <concept>
%   <concept_id>10010520.10010575.10010755</concept_id>
%   <concept_desc>Computer systems organization~Redundancy</concept_desc>
%   <concept_significance>300</concept_significance>
%  </concept>
%  <concept>
%   <concept_id>10010520.10010553.10010554</concept_id>
%   <concept_desc>Computer systems organization~Robotics</concept_desc>
%   <concept_significance>100</concept_significance>
%  </concept>
%  <concept>
%   <concept_id>10003033.10003083.10003095</concept_id>
%   <concept_desc>Networks~Network reliability</concept_desc>
%   <concept_significance>100</concept_significance>
%  </concept>
% </ccs2012>
% \end{CCSXML}
%
% \ccsdesc[500]{Computer systems organization~Embedded systems}
% \ccsdesc[300]{Computer systems organization~Redundancy}
% \ccsdesc{Computer systems organization~Robotics}
% \ccsdesc[100]{Networks~Network reliability}
% \end{verbatim}
%
% You just need to copy this code and paste it in your paper anywhere
% before \verb|\maketitle|.
%
% CCS Concepts and user-defined keywords are required for all articles
% over two pages in length, and are optional for one- and two-page
% articles (or abstracts).
%
% \DescribeMacro{\setcopyright}
% There are several possibilities for the copyright of the papers
% published by the ACM: the authors may license the paper 
% to the ACM, some or all authors might be employees of the
% US or Canadian governments, etc.  Accordingly the command
% \verb|\setcopyright{...}| is introduced.  Its argument is the
% copyright status of the paper, for example,
% \verb|\setcopyright{acmcopyright}|.  The possible values for this
% command are listed in Table~\ref{tab:setcopyright}.  This command
% must be placed in the preamble, before |\begin{document}|.
% Additional information about ACM copyright rules is discussed in
% Section~\ref{sec:ug_cc}.
%
% \begin{table}
%   \centering
%   \caption{Parameters for the \texttt{\textbackslash setcopyright} command}
%   \label{tab:setcopyright}
%   \begin{tabularx}{\textwidth}{lX}
%     \toprule
%     Parameter & Meaning\\
%     \midrule
%     \texttt{none} & The copyright and permission information is not
%     typeset.  (This is the option for some ACM conferences.) \\
%     \texttt{acmlicensed} & The authors retain the copyright but
%     license the publication rights to ACM\@. \\
%     \texttt{rightsretained} & The authors retain the copyright and
%     publication rights to themselves or somebody else. \\
%     \texttt{usgov} & All the authors are employees of the US
%     government. \\
%     \texttt{usgovmixed} & Some authors are employees of the US
%     government. \\
%     \texttt{cagov} & All the authors are employees of the Canadian
%     government. \\
%     \texttt{cagovmixed} & Some authors are employees of the Canadian
%     government. \\
%     \texttt{licensedusgovmixed} & Some authors are employees of the US
%     government, and the publication rights are licensed to ACM\@. \\
%     \texttt{licensedcagov} & All the authors are employees of the Canadian
%     government, and the publication rights are licensed to ACM\@. \\
%     \texttt{licensedcagovmixed} & Some authors are employees of the
%     Canadian
%     government, and the publication rights are licensed to ACM\@. \\
%     \texttt{othergov} & Authors are employees of a
%     government other than the US or Canada. \\
%     \texttt{licensedothergov} & Authors are employees of a
%     government other than the US or Canada, and the publication rights
%                                   are licensed to ACM\@. \\
%     \texttt{iw3c2w3} & Special statement for conferences organized
%     by IW3C2.\\
%     \texttt{iw3c2w3g} & Special statement for conferences organized
%     by IW3C2, when some authors are approved Google employees.\\
%     \texttt{cc} & Creative Commons license.  If this key is set,
%     \textsl{doclicense} images are used to typeset the license. See
%     also \cs{setcctype} command.  \\
%     \texttt{acmcopyright} & The authors transfer the copyright to the
%     ACM.  This choice is no longer available for the
%     non-commissioned authors and will trigger a warning.\\
%     \bottomrule
%   \end{tabularx}
% \end{table}
%
% \DescribeMacro{\setcctype}%
%  If Creative Commons license is used, the package by default chooses
%  CC-BY 4.0 Attribution 4.0 International license.  You can override
%  this choice by the command \cs{setcctype}\oarg{version}\marg{type},
%  where \oarg{version} can be either 3.0 or 4.0 (4.0 by default), and
%  \marg{type} can be one of |zero|, |by|, |by-sa|, |by-nd|,
%  |by-nc|, |by-nc-sa|, |by-nc-nd| (see
%  \url{https://creativecommons.org/licenses/} for the explanation).
%  This command should be used in the preamble only.
%
%  Material published under Creative Commons license should include
%  the corresponding icon.  A modern \TeX\ distribution includes these
%  icons in the package \textsl{doclicense}. In case your distribution
%  does not have them, ACM provides a file \path{ccicons.zip} with
%  these icons.  Just unzip it in the same directory where your
%  document is.
%
% The ACM submission software should generate the right command for you
% to paste into your file.
%
%
% \DescribeMacro{\copyrightyear}%
% Each copyright statement must have the year of copyright.  By
% default it is the same as \cs{acmYear}, but you can override this
% using the macro \cs{copyrightyear}, e.g.,
% \begin{verbatim}
% \acmYear{2016}
% \copyrightyear{2015}
% \end{verbatim}
%
% There is a special case for a personal copy that the authors may be
% allowed to generate for their use or a posting on a personal site
% (check the instructions for the specific journal or conference for
% the details).  The document option |authorversion=true| produces a
% special form of the copyright statement for this case.  Note that
% you still need the \cs{setcopyright} command and (optionally)
% \cs{copyrightyear} command to tell \TeX\ about the copyright owner and
% year.  Also, you should be aware that due to the different sizes of
% the permssion blocks for the printed version and authors' version,
% the page breaks might be different between them.
%
% \DescribeEnv{abstract}%
% The environment |abstract| must \emph{precede} the \cs{maketitle}
% command.  Again, this is different from the standard \LaTeX.
% Putting |abstract| after \cs{maketitle} will trigger an error.
%
%
% \DescribeEnv{teaserfigure}%
% A special kind of figure is used for many two-column conference
% proceedings.  This figure is placed just after the authors but
% before the main text.  The environment |teaserfigure| is used for these
% figures.  This environment must be used \emph{before}
% \cs{maketitle}, for example,
% \begin{verbatim}
% \begin{teaserfigure}
%   \includegraphics[width=\textwidth]{sampleteaser}
%   \caption{This is a teaser}
%   \label{fig:teaser}
% \end{teaserfigure}
% \end{verbatim}
%
%
% \DescribeMacro{\settopmatter}%
% Some information in the top matter is printed for certain journals
% or proceedings and suppressed for others.  You can override these
% defaults using the command \cs{settopmatter}\marg{settings}.  The
% settings and their meanings are listed in
% Table~\ref{tab:settopmatter}.  For example,
% \begin{verbatim}
% \settopmatter{printacmref=false, printccs=true, printfolios=true}
% \end{verbatim}
% The parameter |authorsperrow| requires some explanation.  In
% conference proceedings authors' information is typeset in boxes,
% several boxes per row (see |sample-sigconf.pdf|,
% |sample-sigplan.pdf|, etc.).  The number of boxes per row is
% determined automatically.  If you want to override this,
% you can do it using this parameter, for example,
% \begin{verbatim}
% \settopmatter{authorsperrow=4}
% \end{verbatim}
% However, in most cases you should \emph{not} do this and should use the
% default settings.  Setting |authorsperrow| to $0$ will revert it to the
% default settings.
%
% The parameter |printacmref| specifies whether to print the ACM
% bibliographic entry (default), or not.  Note that this entry is
% required for all articles over one page in length, and is optional
% for one-page articles (abstracts).
%
% \begin{table}
%   \centering
%   \caption{Settings for the \cs{settopmatter} command}
%   \label{tab:settopmatter}
%   \begin{tabularx}{\textwidth}{llX}
%     \toprule
%     Parameter & Values & Meaning\\
%     \midrule
%     |printccs| & true/false & Whether to print CCS categories\\
%     |printacmref| & true/false & Whether to print the ACM bibliographic
%     entry\\
%     |printfolios| & true/false & Whether to print page numbers
%     (folios)\\
%     |authorsperrow| & numeric & Number of authors per row for the title
%     page in
%     conference proceedings formats\\
%     \bottomrule
%   \end{tabularx}
% \end{table}
%
%
% \DescribeMacro{\received}%
% The command \cs{received}\oarg{stage}\marg{date} sets the history of
% the publication.  The~\oarg{stage} argument is optional; the default
% is |Received| for the first date and |revised| for the subsequent
% ones.  For example,
% \begin{verbatim}
% \received{20 February 2007}
% \received[revised]{12 March 2009}
% \received[accepted]{5 June 2009}
% \end{verbatim}
%
%
% \DescribeMacro{\maketitle}%
% The macro \cs{maketitle} must be the last command in the top-matter
% group.  That is it must follow the commands defined in this section.
%
%
% \DescribeMacro{\shortauthors}%
% \emph{After} the command \cs{maketitle}, the macro \cs{shortauthors}
% stores the names of the authors for the running head.  You can
% redefine it if the list of author's name is too long, e.g.,
% \begin{verbatim}
% \maketitle
% \renewcommand{\shortauthors}{Zhou et al.}
% \end{verbatim}
%
%
%\subsection{Top matter of ACM Engage materials}
%\label{sec:ug_engage}
%
% ACM Engage materials resemble conference proceedings, but have some
% special features.  First, as a rule, they are released under a
% Creative Commons license.  By default CC-BY is used.  However, if
% you want to use another variant of CC license, use \cs{setcctype}
% command, for example, |\setcctype{by-nc}|.  Second, abstract is
% called \emph{synopsis}.  Third, there are special top matter items
% used for the materials, such as \emph{Course,} \emph{Resource Type,}
% \emph{Programming Language,} \emph{CS Topics}.
%
% \DescribeMacro{\setengagemetadata}%
% These items are set with the command
% \cs{setengagemetadata}\marg{name}\marg{value}, for example,
% \begin{verbatim}
% \setengagemetadata{Course}{CS1}
% \setengagemetadata{Programming Language}{Python}
% \setengagemetadata{Knowledge Unit}{Programming Concepts}
% \setengagemetadata{CS Topics}{Functions, Data Types, Expressions,
% Mathematical Reasoning}
% \end{verbatim}
%
% Note that the type of Creative Commons license, if such license is
% used, is automatically added to the metadata.
%
%
%\subsection{ACM cover page}
%\label{sec:ug_acmcp}
%
% ACM cover pages are forms of extended abstracts that are added to
% journals at the late stage.  Authors prepare them as separate
% \texttt{.tex} files using |acmcp| format.  At present only JDS uses
% them, but in the future this may change.
%
% There are several top matter commands specific for this format.
%
% \DescribeMacro{\acmArticleType}%
% There are five article types accepted by JDS: \emph{Research} (the
% default), \emph{Review}, \emph{Discussion}, \emph{Invited}, and
% \emph{Position}. The command \cs{acmArticleType}\marg{type} sets the
% article type, for example
% \begin{verbatim}
% \acmArticleType{Review}
% \end{verbatim}
%
% \DescribeMacro{\acmCodeLink}%
% \DescribeMacro{\acmDataLink}%
% The commands \cs{acmCodeDataLink}\marg{link} and
% \cs{acmDataLink}\marg{link} set the links to the data and code
% accompanying the paper, for example,
% \begin{verbatim}
% \acmCodeLink{https://github.com/repository/code}
% \acmDataLink{https://datadryad.org/stash/dataset/doi:DOI}
% \end{verbatim}
% You may repeat these commands if you have several repositories.
%
%
% \cs{acmContributions}%
% The command \cs{acmContributions}\marg{contributions} sets the
% contributions of the authors, for example,
% \begin{verbatim}
% \acmContributions{AW designed the study, CD performed it, all
% authors contributed to the writing.}
% \end{verbatim}
%
% ACM cover page should have the following obligatory sections:
% \begin{itemize}
% \item Problem statement,
% \item Methods,
% \item Results,
% \item Significance.
% \end{itemize}
% 
% Sometimes the addresses extracted from the authors' data are too
% long to fit on the page.  In this case the command
% \cs{authorsaddresses} can be use to override them, for example,
% \begin{verbatim}
% \authorsaddresses{Corresponding author: Ben Trovato,
% \href{mailto:<EMAIL>}{<EMAIL>};
% Institute for Clarity in Documentation, P.O. Box 1212, Dublin,
% Ohio, USA, 43017-6221} 
% \end{verbatim}
% 
% The design of the cover page may require additional runs of latex to
% make the elements of the page align.
%
%\subsection{Internationalization}
%\label{sec:ug_i13n}
%
% ACM accepts publications in languages other than English, as well as
% papers in English with translations of titles, subtitles, keywords
% and abstracts into other languages.  Papers in languages other than
% English usually have titles, subtitles (if applicable), keywords and
% abstracts in English.  Note that CCS concepts are always typeset in
% English.
%
% To submit these papers you need to set the option |language| in the
% \cs{documentclass} command.  This option can be repeated, for
% example, 
% \begin{verbatim}
% \documentclass[sigconf, language=french, language=english]{acmart}
% \end{verbatim}
% The last language in the list is the main language of the paper,
% i.e. the one for the main title, abstract, body, etc.  The other
% languages are \emph{secondary,} and used for translated titles,
% keywords, abstracts.  Thus the paper above is written in English,
% and has a secondary abstract and a secondary title in French.  On
% the other hand, a paper in French with secondary titles and abstracts
% in English and German should use, for example
% \begin{verbatim}
% \documentclass[sigconf,
%                language=german, 
%                language=english, 
%                language=french]{acmart}
% \end{verbatim}
% 
%
% This key can use any language defined in \textsl{babel}
% package~\cite{Braams22:Babel} (currently the package is tested with
% English, French, German and Spanish languages; other languages may
% require a translation of \cs{keywordsname} macro).  Actually
% \textsl{acmart} loads \textsl{babel} internally, so you can use  the
% facilities provided by this package.
%
%
% If this key is set, you have access to several additional top matter
% commands.
%
% \DescribeMacro{\translatedtitle}%
% \DescribeMacro{\translatedsubtitle}%
% \DescribeMacro{\translatedkeywords}%
% The commands \cs{translatedtitle}\marg{language}{title},
% \cs{translatedsubtitle}\marg{language}{subtitle} and
% \cs{translatedkeywords}{language}{keywords} are used to set title,
% subtitle and keywords in the secondary language.  For example, a
% paper in English with French title and abstract may set 
% \begin{verbatim}
% \title{A note on computational complexity}
% \translatedtitle{french}{Remarque sur la complexit\'e de calcul}
% \end{verbatim}
% while a paper in French should set
% \begin{verbatim}
% \title{Remarque sur la complexit\'e de calcul}
% \translatedtitle{english}{A note on computational complexity}
% \end{verbatim}
%
% \DescribeEnv{translatedabstract}%
% Similarly, |translatedabstract| environment has a mandatory language
% argument, for example,
% \begin{verbatim}
% \begin{translatedastract}{english}
%   This is the English version of the abstract
% \end{translatedastract}
% \end{verbatim}
%
% You can repeat these commands if a paper has more than one secondary
% language. 
%
% Use the standard commands (\cs{title}, \cs{subtitle},
% \cs{keywords}, |abstract|) for the main language of the paper.  
%
%\subsection{Algorithms}
%\label{sec:ug_algorithms}
%
% There are now several good packages for typesetting
% algorithms~\cite{Fiorio15, Brito09, Heinz15}, and the authors are
% free to choose their favorite one.
%
%
%
%\subsection{Figures and tables}
%\label{sec:ug_floats}
%
% The new ACM styles use the standard \LaTeX\ interface for figures and
% tables.  There are some important items to be aware of, however.
%
% \begin{enumerate}
% \item The captions for figures must be entered \emph{after} the
% figure bodies and for tables \emph{before} the table bodies.
% \item The ACM uses the standard types for figures and tables and adds
% several new ones.  In total there are the following types:
% \begin{description}
% \item[figure, table:] a standard figure or table taking a full text
% width in one-column formats and one column width in two-column formats.
% \item[figure*, table*] in two-column formats, a special figure or
% table taking a full text width.
% \item[teaserfigure:] a special figure before \cs{maketitle}.
% \end{description}
%
% \item Accordingly, when scaling images, one should use the
% following sizes:
% \begin{enumerate}
% \item For |teaserfigure|, |figure| in one-column mode or |figure*| in
%   two-column mode, use \cs{textwidth}.  In one-column mode, you can also
%   use \cs{columnwidth}, which coincides with \cs{textwidth} in this
%   case.
% \item For |figure| in two-column mode, use \cs{columnwidth}.
% \end{enumerate}
%
% \end{enumerate}
%
% It is strongly recommended to use the package |booktabs|~\cite{Fear05}
% and follow its main principles of typography with respect to tables:
% \begin{enumerate}
% \item Never, ever use vertical rules.
% \item Never use double rules.
% \end{enumerate}
% It is also a good idea not to overuse horizontal rules.
%
% For table \emph{footnotes} you have several options described in the TeX
% FAQ~\cite{TeXFAQ}. The simplest one is to use a \cs{minipage}
% environment:
% \begin{verbatim}
% \begin{table}
% \caption{Simulation Configuration}
% \label{tab:conf}
% \begin{minipage}{\columnwidth}
% \begin{center}
% \begin{tabular}{ll}
%   \toprule
%   TERRAIN\footnote{This is a table footnote. This is a
%     table footnote. This is a table footnote.} &
%     (200\,m$\times$200\,m) Square\\
%   Node Number     & 289\\
%   Node Placement  & Uniform\\
%   Application     & Many-to-Many/Gossip CBR Streams\\
%   Payload Size    & 32 bytes\\
%   Routing Layer   & GF\\
%   MAC Layer       & CSMA/MMSN\\
%   Radio Layer     & RADIO-ACCNOISE\\
%   Radio Bandwidth & 250Kbps\\
%   Radio Range     & 20m--45m\\
%   \bottomrule
% \end{tabular}
% \end{center}
% \bigskip
% \footnotesize\emph{Source:} This is a table
%  sourcenote. This is a table sourcenote. This is a table
%  sourcenote.
%
%  \emph{Note:} This is a table footnote.
% \end{minipage}
% \end{table}
% \end{verbatim}
%
%
% Tables and figures are by default centered.  However, in some cases
% (for example, when you use several subimages per figure) you may
% need to override this.  A good way to do so is to put the contents
% into a \cs{minipage} of the width \cs{columnwidth}.
%
%
%\subsection{Descriptions of images}
%\label{sec:descriptions}
%
% \DescribeMacro{\Description}%
% Some readers of ACM publications might be visually challenged.
% These readers might use a voice-over software to read aloud the
% papers.  It is important to provide them a description of each
% image used in the paper.
%
% The command \cs{Description}\oarg{short description}\marg{long
% description} should be placed inside every \texttt{figure},
% \texttt{teaserfigure} or \texttt{marginfigure} environment to
% provide a description of the image(s) used in the figure.  Unlike
% \cs{caption}, which is used alongside the image, \cs{Description} is
% intended to be used instead of the image, for example,
% \begin{verbatim}
% \begin{figure}
%   \centering
%   \includegraphics{voltage}
%   \Description{A bell-like histogram centered at $0.5$~V with most
%   measurements between $0.2$~V and $0.8$~V}
%   \caption{Histogram of the measurements of voltage}
%   \label{fig:voltage}
% \end{figure}
% \end{verbatim}
% At present the lack of descriptions generates a warning at
% compilation.
%
%\subsection{Theorems}
%\label{sec:ug_theorems}
%
% The ACM classes define two theorem styles and several pre-defined
% theorem environments:
% \begin{description}
% \item[acmplain:] this is the style used for
%   |theorem|,
%   |conjecture|,
%   |proposition|,
%   |lemma| and
%   |corollary|, and
% \item[acmdefinition:] this is the style used for
%   |example| and
%   |definition|.
% \end{description}
%
%
% These environments are defined by default.  In the unusual
% circumstance that a user does not wish to have these environments
% defined, the option |acmthm=false| in the preamble will suppress
% them.
%
% Sometimes authors want to define new theorem-like constructs that
% use |theorem| counters.  These constructs must be defined either after
% |\begin{document}|, or delayed using \cs{AtEndPreamble} macro,
%   for example,
% \begin{verbatim}
% \AtEndPreamble{%
%   \theoremstyle{acmdefinition}
%   \newtheorem{remark}[theorem]{Remark}}
% \end{verbatim}
% 
%
%\subsection{Online-only and offline-only material}
%\label{sec:ug_screen}
%
% \DescribeEnv{printonly}%
% \DescribeEnv{screenonly}%
% Some supplementary material in ACM publications is put online but
% not in the printed version.  The text inside the environment
% |screenonly| will be typeset only when the option |screen| (see
% Section~\ref{sec:invocation}) is set to |true|.  Conversely, the
% text inside the environment |printonly| is typeset only when this
% option is set to |false|.  For example,
% \begin{verbatim}
% \section{Supplementary materials}
%
% \begin{printonly}
%   Supplementary materials are available in the online version of this paper.
% \end{printonly}
%
% \begin{screenonly}
%   (The actual supplementary materials.)
% \end{screenonly}
% \end{verbatim}
%
% We use the |comment| package for typesetting this code, so
% |\begin| and |\end| should start on a line of their own with
% no leading or trailing spaces.
%
%\subsection{Note about anonymous mode}
%\label{sec:ug_anonymous}
%
% \DescribeEnv{anonsuppress}%
% When the option |anonymous| is selected, \TeX\ suppresses author
% information (including the number of authors) for an anonymous review.
% However, sometimes the information identifying the authors may be
% present in the body of the paper.  For example,
% \begin{verbatim}
% \begin{anonsuppress}
%   This is the continuation of the previous work by the author
%   \cite{prev1, prev2}.
% \end{anonsuppress}
% \end{verbatim}
%
% As for the |printonly| and |screenonly| environments,
% |\begin{anonsuppress}| and |\end{anonsuppress}| should start on a
% line of their own with no leading or trailing spaces.
%
% \DescribeMacro{\anon}%
% To suppress short snippets of information, use the command
% \cs{anon}\oarg{substitute}\marg{suppressed-text}.  By default
% \oarg{substitute} is the word ANONYMOUS. Examples:
% \begin{verbatim}
% This work was performed at \anon{NSA}.
% This work was performed at \anon[No Such Agency]{NSA}.
% \end{verbatim}
% 
%
%\subsection{Acknowledgments}
%\label{sec:ug_acks}
%
% The traditional ``Acknowledgments'' section is conventionally used
% to thank persons and granting agencies for their help and support.
% However, there are several important considerations about this
% section.
%
% First, in anonymous mode this section must be omitted: it gives
% too much information to reviewers.  Second, data about
% grants is extracted and stored separately by the postprocessing
% software.  ACM classes provide facilities for both these tasks.
%
% \DescribeEnv{acks}%
% The environment |acks| starts an unnumbered section
% ``Acknowledgments'' unless the anonymous mode is chosen.  Put all
% thanks inside this environment.
%
% As for the |printonly| and |screenonly| environments,
% |\begin{acks}| and |\end{acks}| should start on a
% line of their own with no leading or trailing spaces or comments. 
%
% \DescribeMacro{\grantsponsor}%
% \DescribeMacro{\grantnum}%
% All financial support \emph{must} be listed using the commands
% \cs{grantsponsor} and \cs{grantnum}.  These commands tell the
% postprocessing software about the granting organization and
% grant.  The format of these commands is the following:
% \begin{quote}
%   \cs{grantsponsor}\marg{sponsorID}\marg{name}\marg{url}\\
%   \cs{grantnum}\oarg{url}\marg{sponsorID}\marg{number}.
% \end{quote}
% Here \marg{sponsorID} is the unique ID used to match grants to
% sponsors, \marg{name} is the name of the sponsor, \marg{url} is its
% URL, and \marg{number} is the grant number.  The \marg{sponsorID} of
% the \cs{grantnum} command must correspond to the \marg{sponsorID} of a
% \cs{grantsponsor} command.  Some awards have their own web pages,
% which you can include using the optional argument of the \cs{grantnum}
% command.
%
% At present \marg{sponsorID} is chosen by the authors and can be an
% arbitrary key in the same way the label of a \cs{cite} is arbitrarily
% chosen.  There might be a change to this policy if the ACM decides to
% create a global database of sponsoring organizations.
%
% Example:
% \begin{verbatim}
% \begin{acks}
%   The authors would like to thank Dr. Yuhua Li for providing the
%   matlab code of the \textit{BEPS} method.
%
%   The authors would also like to thank the anonymous referees for
%   their valuable comments and helpful suggestions. This work is
%   supported by the \grantsponsor{my-grant-GS501100001809}{National Natural
%   Science Foundation of
%   China}{https://doi.org/10.13039/501100001809} under Grant
%   No.:~\grantnum{GS501100001809}{61273304}
%   and~\grantnum[http://www.nnsf.cn/youngscientists]{GS501100001809}{Young
%   Scientists' Support Program}.
% \end{acks}
% \end{verbatim}
%
%
%\subsection{Bibliography}
%\label{sec:ug_bibliography}
%
% The ACM lets you use either Bib\TeX\ or Bib\LaTeX\ to process your references:
% they require slightly different setup of your \LaTeX\ file, as detailed in
% the following subsections.
%\subsubsection{Processing using Bib\TeX}
% This uses the |natbib| package for formatting references and
% the Bib\TeX\ style file \path{ACM-Reference-Format.bst} for Bib\TeX\
% processing.  You can disable loading of |natbib| using the
% option |natbib=false| in \cs{documentclass}.  However, it is not
% recommended, as well as the use of Bib\TeX\ styles other than
% \path{ACM-Reference-Format.bst}, and may delay the processing of the
% manuscript.
%
%
% \DescribeMacro{\citestyle}%
% If you use |natbib|, you can select one of two predefined citation
% styles using the command \cs{citestyle}: the author-year format
% |acmauthoryear| or the numeric format |acmnumeric|.  For example,
% \begin{verbatim}
% \citestyle{acmauthoryear}
% \end{verbatim}
% Note that numeric citations are the default mode for most formats.
%
% \DescribeMacro{\setcitestyle}%
% You can further customize |natbib| using
% the \cs{setcitestyle} command, for example,
% \begin{verbatim}
% \setcitestyle{numbers,sort&compress}
% \end{verbatim}
%
% One of the more common versions is
% \begin{verbatim}
% \setcitestyle{nosort}
% \end{verbatim}
% It is useful if you do not like the way |natbib| sorts citation
% lists.
%
% If you use |natbib|, then commands like \cs{citep} and
% \cs{citeauthor} are automatically supported.  The command
% \cs{shortcite} is the same as \cs{cite} in numerical mode and cites
% the year in author-date mode.
%
% Note that before version~1.48 the command \cs{citeyear} put the year
% in parentheses.  In version~1.48 and later it produces just the
% year; the command \cs{citeyearpar} can be used to emulate its old
% behavior.
%
% There are several customized \BibTeX\ entry types and fields in the ACM
% style file \path{ACM-Reference-Format.bst} that you may want to be
% aware of.  You can find many examples on the Web page
% \url{https://www.acm.org/publications/authors/bibtex-formatting}. 
%
%
%\subsubsection{URL, DOI, eprint}
%
%
%
% The style supports the fields \path{doi} and \path{url}, for example,
% \begin{verbatim}
%  doi =          "10.1145/1188913.1188915",
%  url =          "http://ccrma.stanford.edu/~jos/bayes/bayes.pdf",
% \end{verbatim}
% Normally the printing of URL is suppressed if DOI is present.
% However, there is a special field \path{distinctURL}.  If it is
% present and is not zero, URL is printed even if DOI is present.
%
% The dynamic nature of Internet makes URLs not permanent: sometimes
% they disappear or change.  The problem of ``stale links'' exists
% from the early days of WWW.  That is why ACM prefers DOIs to URLs:
% unlike the latter, the former are supposed to work forever.
% However, if you really need to use URLs, there are two facilities
% that help to mitigate some problems with the URLs.  The field
% \path{lastaccessed} gives the date when the given URL was visited
% and had the information you saw.  Presumably, some service like
% Internet Wayback Machine (\url{https://web.archive.org/}) allows the
% reader to retrieve this link at the given date.  Moreover, sometimes
% a page is explicitly archived by this or other service.  In this
% case the field \path{archived} will give the reference ot the
% archival version.  Examples:
% \begin{verbatim}
% lastaccessed = {March 2, 2005},
% archived = {https://web.archive.org/web/20240505055615}
% \end{verbatim}
% 
%
% The style supports the arXiv-recommended fields \path{eprint} and
% (optionally) \path{primaryclass}, for example,
% \begin{verbatim}
%  eprint =       "960935712",
%  primaryclass = "cs",
% \end{verbatim}

%\subsubsection{Special entry types}
%\label{sec:preprints}
%
%  
% 
% 
%
% There are several  special entry types. Types \path{online} and
% \path{game} are used for Web pages and games, for example,
% \begin{verbatim}
% @online{Thornburg01,
%  author =       "Harry Thornburg",
%  year =         "2001",
%  title =        "Introduction to Bayesian Statistics",
%  url =          "http://ccrma.stanford.edu/~jos/bayes/bayes.html",
%  month =        mar,
%  lastaccessed = "March 2, 2005",
% }
% \end{verbatim}
% Entry types \path{artifactsoftware}, \path{artifactdataset}
% (with synonyms \path{software} and \path{dataset}) can be used to
% cite software artifacts and datasets, for example,
% \begin{verbatim}
% @ArtifactSoftware{R,
%    title = {R: A Language and Environment for Statistical Computing},
%    author = {{R Core Team}},
%    organization = {R Foundation for Statistical Computing},
%    address = {Vienna, Austria},
%    year = {2019},
%    url = {https://www.R-project.org/},
%}
% @ArtifactDataset{UMassCitations,
%  author    =  {Sam Anzaroot and Andrew McCallum},
%  title     =  {{UMass} Citation Field Extraction Dataset},
%  year      = 2013,
%  url       =
%     {http://www.iesl.cs.umass.edu/data/data-umasscitationfield},
%  lastaccessed = {May 27, 2019}
% }
% \end{verbatim}
%
%
% For these entry types you can use the \path{lastaccessed} field to add
% the access date for the URL and \path{archived} field to add the
% archival link.  
%
%
%
% There are two ways to enter video or audio sources in the
% bibliograpy corresponding to two different possibilies.  For
% standalone sources available online, you can use an \path{online}
% entry and set its \path{howpublished} field.  For example,
% \begin{verbatim}
% @online{Obama08,
%  author =       "Barack Obama",
%  year   =       "2008",
%  title  =       "A more perfect union",
%  howpublished = "Video",
%  day    =       "5",
%  url    =       "http://video.google.com/videoplay?docid=6528042696351994555",
%  month  =       mar,
%  lastaccessed = "March 21, 2008",
% }
% \end{verbatim}
%
% For sources available as supplemental material attached to
% conference proceedings and similar documents, you can use the usual
% \path{inproceedings} entry type and set its \path{howpublished}
% field:
% \begin{verbatim}
% @Inproceedings{Novak03,
%  author =       "Dave Novak",
%  title =        "Solder man",
%  booktitle =    "ACM SIGGRAPH 2003 Video Review on Animation theater Program",
%  year =         "2003",
%  publisher =    "ACM Press",
%  address =      "New York, NY",
%  pages =        "4",
%  month =        "March 21, 2008",
%  doi =          "10.9999/woot07-S422",
%  howpublished = "Video",
% }
% \end{verbatim}
%
% Sometimes you need to cite a complete issue of a journal.  The
% \path{periodical} entry type is intended for this:
% \begin{verbatim}
% @periodical{JCohen96,
%  key =          "Cohen",
%  editor =       "Jacques Cohen",
%  title =        "Special issue: Digital Libraries",
%  journal =      "Communications of the {ACM}",
%  volume =       "39",
%  number =       "11",
%  month =        nov,
%  year =         "1996",
% }
% \end{verbatim}
%
% Sometimes a work is presented at meeting, but is not (yet)
% published in the proceedings.  The entry type \path{presentation} is
% used for this purpose.  The field \path{venue} is used for the place
% of the meeting:
% \begin{verbatim}
% @Presentation{Reiser2014,
%   author = {Brian J. Reiser},
%   year = 2014,
%   title = {Designing coherent storylines aligned with NGSS for the
%            K-12 classroom},
%   venue = {National Science Education Leadership Association
%         Meeting, Boston, MA, USA},
%   url = {https://www.academia.edu/6884962/}
%  }
% \end{verbatim}
%  The URL, if present, may be accompanied by \path{lastaccessed} and
%  \path{archived} links.
%
% A preprint can be cited using the entry
% \path{preprint}, for example,
% \begin{verbatim}
% @preprint{AnzarootPBM14,
%   author    = {Sam Anzaroot and
%                Alexandre Passos and
%                David Belanger and
%                Andrew McCallum},
%   title     = {Learning Soft Linear Constraints with Application to
%                   Citation Field Extraction},
%   year      = {2014},
%   archivePrefix = {arXiv},
%   eprint    = {1403.1349},
%   doi =    {10.48550/arXiv.1403.1349}
% }
% \end{verbatim}
%
% Sometimes you need to cite an article under review.  ACM prefers the
% authors to use preprints for this purpose, but sometimes it is not
% feasible.  In this case you can use the entry \path{underreview},
% for example,
% \begin{verbatim}
% @underreview{Baggett2025,
%   author = {R. Baggett and M. Simecek and C. Chambellan
%             and K. Tsui and M. Fraune},
%   year = 2025,
%   title = {Fluidity in the Phased Framework of Technology
%            Acceptance: Case Study to Gain a Holistic Understanding
%            of (Older Adult) Participant Advancement Through
%            Acceptance Phases with  Mobile Telepresence Robots},
%   journal = {Robotics Aut. Systems}
% }
% \end{verbatim}
%
%
%\subsubsection{Dates and sorting}
%
%
% If you do not know the year of publication, the style will add
% ``[n.\,d.]'' (for ``no date'') to the entry.
%
% If you do not know the author (this is often the case for online
% entries), use the |key| field to add a key for sorting and citations,
% for example,
% \begin{verbatim}
% @online{TUGInstmem,
%  key =          {TUG},
%  year  =        2017,
%  title =        "Institutional members of the {\TeX} Users Group",
%  url =          "http://wwtug.org/instmem.html",
%  lastaccessed = "May 27, 2017",
% }
% \end{verbatim}
%
% A note about sorting.  The current ACM bibliography styles always
% sort the entries according to authors names and publication year.
% There is a controversy about sorting names with ``von'' or ``van''
% part: should Ludwig van Beethoven be sorted under ``V'' or under
% ``B''?  The American practice is to use ``van'' in sorting, i.e. to
% file van Beethoven under ``V''.  However, some authorities recommend
% to sort Dutch persons according to their last names (see
% e.g. \url{https://www.ifla.org/files/assets/cataloguing/pubs/names-of-persons_1996.pdf}).
% While I do not want to take a part in this dispute, I would like to
% point to the old ``noopsort'' trick by Oren Patashnik.  Add to the
% \texttt{.bib} file the line
% \begin{verbatim}
% @PREAMBLE{"\providecommand{\noopsort}[1]{}"}
% \end{verbatim}
% and then encode the author as
% \begin{verbatim}
%  author = {Ludwig {\noopsort{Beethoven}}van Beethoven},
% \end{verbatim}
% This will make the author to be sorted as ``Beethoven'' rather than
% ``van Beethoven''.
%
% The current bst style defines a number of macros for common journal
% names.  In particular, all journals listed in Table~\ref{tab:pubs}
% are includes, so you can use strings like |journal = taccess| for
% \emph{ACM Transactions on Accessible Computing}.
%
%\subsubsection{Processing using Bib\LaTeX}
% You will find in this package two sets of style files for Bib\LaTeX,
% \verb|acmnumeric| and \verb|acmauthoryear|, that mimic the behavior
% of the ACM-Reference-Format.bst Bib\TeX\ style. They provide you
% access to all the power of Bib\LaTeX\ and already include
% support for advanced citation of software artifacts from the
% \verb|biblatex-software| package, also separately available on CTAN.
% Look at the \verb|biblatex-software| documentation to learn more about
% what it offers.
% 
% There are a few key differences in how the \LaTeX\ sources are set up
% when using Bib\LaTeX\ instead of Bib\TeX, that we summarize briefly
% here (please refer to the official Bib\LaTeX\ documentation for more details).
%
% First of all, you need to pass the \verb|natbib=false| option to the document class,
% and remove the \verb|\citestyle{acmauthoryear}| command from the sources (if present).
%
% Then, in the preamble of your document you need to load the Bib\LaTeX\ package
% and select the approriate bibliography style, as follows
% \begin{verbatim}
% \RequirePackage[
%  datamodel=acmdatamodel,
%  style=acmnumeric, % use style=acmauthoryear for publications that require it
%  ]{biblatex}
% \end{verbatim}
%
% Also in the preamble, you need to declare the bibliography sources files
% using the \verb|\addbibresource| command (one \verb|\addbibresource|
% command per source file), e.g.:
% \begin{verbatim}
% \addbibresource{software.bib}
% \addbibresource{sample-base.bib}
% \end{verbatim}
%
% At the end of the document, where you want the bibliography to appear,
% you need to place the command \verb|\printbibliography|.
%
% Look at the \verb|sample-*-biblatex.tex| files that can be found in the samples
% directory after running \verb|make| for templates showcasing
% these Bib\LaTeX\ styles.

%\subsection{Colors}
%\label{sec:ug_colors}
%
% While printed ACM publications are usually black and white, |screen|
% mode allows the use of colors.  The ACM classes pre-define several
% colors according to~\cite{ACMIdentityStandards}:  |ACMBlue|,
% |ACMYellow|, |ACMOrange|, |ACMRed|, |ACMLightBlue|, |ACMGreen|,
% |ACMPurple| and |ACMDarkBlue|.  You can use them in color
% assignments.
%
% The ACM provides the following recommendation on color use.
%
% The most accessible approach would be to ensure that your article is
% still readable when printed in greyscale. The most notable reasons
% for this are:
% \begin{enumerate}
% \item The most common type of inherited Color Vision Deficiency
% (CVD) is red-green (in which similar-brightness colors that differ
% only in their amounts of red or green are often confused), and it
% affects up to 8\% of males and 0.5\% of females of Northern European
% descent.
% \item The most common type of acquired Color Vision Deficiency (CVD)
% is blue-yellow (including mild cases for many older adults).
% \item Most printing is in black and white.
% \item Situational impairments (e.g., bright sunlight shining on a
% mobile screen) tend to reduce the entire color gamut, reducing color
% discriminability.
% \end{enumerate}
%
% \textbf{Note:} It is \emph{not} safe to encode information using
% only variations in color (i.e., only differences in hue and/or
% saturation) as there is bound to be someone affected!
%
% To ensure that you are using the most accessible colors, the ACM
% recommends that you choose sets of colors to help ensure suitable
% variations in when printed in greyscale by using either of the following tools:
% \begin{enumerate}
%  \item ColourBrewer: \url{http://colorbrewer2.org/}
%  \item ACE: The Accessible Colour Evaluator:
%    \url{http://daprlab.com/ace/} for designing WCAG 2.0 compliant
%    palettes.
%  \end{enumerate}
%
%
%
%\subsubsection{Manual bibliography}
%\label{sec:ug_manual_bibliography}
%
% Some people create bibliographies manually, writing down
% \cs{bibitem} commands explicitly.  This approach is \emph{not}
% recommended for ACM styles.  The reason is, ACM submissions, besides
% being typeset, are also processed by special programs that extract
% metadata and references.  Bibliographies created automatically with
% ACM styles contain customized macros for these programs, for
% example,
% \begin{verbatim}
% \bibitem[Ablamowicz and Fauser(2007)]%
%         {Ablamowicz07}
% \bibfield{author}{\bibinfo{person}{Rafal Ablamowicz} {and}
%   \bibinfo{person}{Bertfried Fauser}.} \bibinfo{year}{2007}\natexlab{}.
% \newblock \bibinfo{booktitle}{\emph{CLIFFORD: a Maple 11 Package for Clifford
%   Algebra Computations, version 11}}.
% \newblock
% \urldef\tempurl%
% \url{http://math.tntech.edu/rafal/cliff11/index.html}
% \showURL{%
% Retrieved February 28, 2008 from \tempurl} 
% \end{verbatim}
%
% Manual bibliographies without these macros may slow down the
% publication process, and thus are not recommended for ACM
% submissions. 
%
%\subsection{Other notable packages and typographic remarks}
%\label{sec:ug_other}
%
% Several other packages are recommended for specialized tasks.
%
% The package |subcaption|~\cite{Sommerfeldt13:Subcaption} is
% recommended for complex figures with several subplots or subfigures
% that require separate subcaptioning.  The packages
% |nomencl|~\cite{Nomencl} and
% |glossaries|~\cite{Talbot16:Glossaries} can be used for the
% automatic creation of the lists of symbols and concepts used.
%
%
% By default |acmart| prevents all widows and orphans (i.e., lonely
% lines at the beginning or end of the page) and hyphenation at
% the end of the page.  This is done by the rather strict settings
% \begin{verbatim}
% \widowpenalty=10000
% \clubpenalty=10000
% \brokenpenalty=10000
% \end{verbatim}
% However, this may lead to frustrating results when the authors must
% obey a page limit.  Setting these penalties to smaller values may
% help if you absolutely need to.
%
% Another problem might be the too strict line breaking rules.  Again,
% a strategically placed \cs{sloppy} command or putting the
% problematic paragraph inside \texttt{sloppypar} environment might
% help---but beware, the results might be, well, sloppy.
%
% Note that the uppercasing in section titles is done using
% the |textcase| package~\cite{Carlisle04:Textcase}, so the command
% \cs{NoCaseChange} inside the title may help to prevent extraneous
% uppercasing.
%
%
%\subsection{Counting words}
%\label{sec:ug_counting}
%
% Some ACM conferences use word count limits for papers.  The
% calculation of word number for a paper with math, tables and figures
% is not a trivial task.  Currently the authoritative word count is
% done by translating the PDF to text and using |wc -w| on the
% output.  Authors can use the package |texcount| (used by Overleaf)
% to get an estimate of the word count.  To faciliate this one adds to the
% beginning of the package metacomments
% \begin{verbatim}
% %TC:macro \cite [option:text,text]
% %TC:macro \citep [option:text,text]
% %TC:macro \citet [option:text,text]
% %TC:envir table 0 1
% %TC:envir table* 0 1
% %TC:envir tabular [ignore] word
% %TC:envir displaymath 0 word
% %TC:envir math 0 word
% %TC:envir comment 0 0
% \end{verbatim}
% and uses |\begin{math}...\end{math}| instead of dollar signs for
% math.  Note that the count is in any case approximate, and the final
% decision of editors is based on PDF count.
%
% The script |texcount| provides a report of word count in the
% document.
%
%
%
%\subsection{Disabled or forbidden commands}
%\label{sec:ug_disabled}
%
% The goal of |acmart| package is to provide a uniform look and feel
% for ACM publications.  Accordingly, a number of commands is
% forbidden or disabled in |acmart|.
%
% You may \emph{not} put several authors or several e-mails into a
% \cs{author} or \cs{email} command.  This may lead to errors or
% warning.
%
% You cannot change \cs{baselinestretch} in your document: this
% produces an error.
%
% You should not abuse the command \cs{vspace}:  this command may
% disturb the typesetting of ACM papers.
%
% You should not load |amssymb| package since the package |acmart|
% defines the corresponding symbols itself.
%
%\subsection{Notes for wizards}
%\label{sec:ug_preload}
%
% Sometimes you need to change the behavior of |acmart|.  The
% usual way to do this is to redefine commands in the preamble.
% However, these definitions are executed \emph{after} |acmart| is
% loaded and certain decisions are made.  This presents a number of
% problems.
%
% For example, one may want to use the |titletoc| package with |acmart|.
% This package should be loaded before |hyperref|.  However, since
% |acmart| loads |hyperref| itself, the line |\usepackage{titletoc}|
% in the preamble will lead to grief (see
% \url{http://tex.stackexchange.com/questions/357265/using-titletoc-with-acm-acmart-style}).
%
% Another example is passing options to a package.  Suppose you want to
% use the |dvipsnames| option of the |xcolor| package.  Normally you cannot do
% this because |acmart| loads this package itself without options.
%
% The file |acmart-preload-hook.tex| can be used to solve these
% problems.  If this file exists, it will be processed before any other
% package.  You can use this file to load packages or pass options to
% them.  For example, if you put in this file
% \begin{verbatim}
% \let\LoadClassOrig\LoadClass
% \renewcommand\LoadClass[2][]{\LoadClassOrig[#1]{#2}%
% \usepackage{titletoc}}
% \end{verbatim}
% then |titletoc| will be loaded before |hyperref|.  If you put in
% this file
% \begin{verbatim}
% \PassOptionsToPackage{dvipsnames}{xcolor}
% \end{verbatim}
% you will pass |dvipsnames| to |xcolor|.
%
% \textbf{Important note.}  This hook makes it too easy to create a
% manuscript that is not acceptable by the ACM.  It is even easier to
% create a file that cannot be compiled.  So please do not use it
% \emph{unless you know what you are doing.}  And if you use it,
% \emph{do not ask for support.}  If you decide to use this hook, you
% are on your own.
%
% \DescribeMacro{\AtBeginMaketitle}%
% Another hook is \cs{AtBeginMaketitle}.  The commands in this hook
% are executed before \cs{maketitle}.
%
%
%\subsection{Currently supported publications}
%\label{sec:pubs}
%
%\bgroup\centering
% \begin{longtable}{>{\ttfamily}p{0.2\textwidth}@{}p{0.8\textwidth}}
%   \caption{ACM publications and arguments of the \cs{acmJournal}
%   command}
%   \label{tab:pubs}\\
%     \toprule
%     \normalfont Abbreviation & Publication \\
%     \midrule
% \endfirsthead
%   \caption[]{ACM publications and arguments of the \cs{acmJournal}
%   command (continued)}\\
%     \toprule
%     \normalfont Abbreviation & Publication \\
%     \midrule
% \endhead
% \bottomrule
%     \endfoot
%     ACMJCSS &  ACM Journal on Computing and Sustainable Societies \\
%     CIE & ACM Computers in Entertainment \\
%     CSUR & ACM Computing Surveys\\
%     DLT & Distributed Ledger Technologies: Research and Practice\\
%     DGOV & Digital Government: Research and Practice \\
%     DTRAP &  Digital Threats: Research and Practice\\
%     FAC & Formal Aspects of Computing \\
%     GAMES & ACM Games: Research and Practice\\
%     HEALTH & ACM Transactions on Computing for Healthcare\\
%     IMWUT & PACM on Interactive, Mobile, Wearable and Ubiquitous
%     Technologies\\
%     JACM &  Journal of the ACM \\
%     JATS &  ACM Journal on Autonomous Transportation Systems \\
%     JDIQ & ACM Journal of Data and Information Quality \\
%     JDS & ACM/IMS Journal of Data Science \\
%     JEA & ACM Journal of Experimental Algorithmics \\
%     JERIC & ACM Journal of Educational Resources in Computing\\
%     JETC & ACM Journal on Emerging Technologies in Computing Systems \\
%     JOCCH & ACM Journal on Computing and Cultural Heritage \\
%     JRC & ACM Journal on Responsible Computing \\
%     PACMCGIT & Proceedings of the ACM on Computer Graphics and
%     Interactive Techniques\\
%     PACMHCI & PACM on Human-Computer Interaction\\
%     PACMOD & PACM on Management of Data\\
%     PACMNET & PACM on Networking\\
%     PACMPL & PACM on Programming Languages \\
%     PACMSE & PACM on Software Engineering \\
%     POMACS & PACM on Measurement and Analysis of Computing Systems \\
%     TAAS & ACM Transactions on Autonomous and Adaptive Systems\\
%     TACCESS & ACM Transactions on Accessible Computing\\
%     TACO & ACM Transactions on Architecture and Code Optimization \\
%     TALG & ACM Transactions on Algorithms \\
%     TALLIP & ACM Transactions on Asian and Low-Resource Language
%     Information Processing\\
%     TAP & ACM Transactions on Applied Perception \\
%     TCPS & ACM Transactions on Cyber-Physical Systems\\
%     TDS & ACM/IMS Transactions on Data Science\\
%     TEAC & ACM Transactions on Economics and Computation\\
%     TECS & ACM Transactions on Embedded Computing Systems \\
%     TELO & ACM Transactions on Evolutionary Learning and Optimization\\
%     THRI & ACM Transactions on Human-Robot Interaction\\
%     TIIS & ACM Transactions on Interactive Intelligent Systems\\
%     TIOT & ACM Transactions on Internet of Things \\
%     TISSEC & ACM Transactions on Information and System Security\\
%     TIST & ACM Transactions on Intelligent Systems and Technology \\
%     TKDD & ACM Transactions on Knowledge Discovery from Data\\
%     TMIS & ACM Transactions on Management Information Systems\\
%     TOCE & ACM Transactions on Computing Education\\
%     TOCHI & ACM Transactions on Computer-Human Interaction\\
%     TOCL & ACM Transactions on Computational Logic\\
%     TOCS & ACM Transactions on Computer Systems \\
%     TOCT & ACM Transactions on Computation Theory \\
%     TODAES & ACM Transactions on Design Automation of Electronic Systems\\
%     TODS & ACM Transactions on Database Systems\\
%     TOG & ACM Transactions on Graphics\\
%     TOIS & ACM Transactions on Information Systems\\
%     TOIT & ACM Transactions on Internet Technology\\
%     TOMACS & ACM Transactions on Modeling and Computer Simulation \\
%     TOMM  & ACM Transactions on Multimedia Computing, Communications
%     and Applications \\
%     TOMPECS & ACM Transactions on Modeling and Performance Evaluation
%     of Computing Systems\\
%     TOMS & ACM Transactions on Mathematical Software\\
%     TOPC & ACM Transactions on Parallel Computing\\
%     TOPLAS & ACM Transactions on Programming Languages and Systems\\
%     TOPML & ransactions on Probabilistic Machine Learning\\
%     TOPS & ACM Transactions on Privacy and Security\\
%     TORS & ACM Transactions on Recommender Systems\\
%     TOS & ACM Transactions on Storage\\
%     TOSEM & ACM Transactions on Software Engineering and Methodology\\
%     TOSN & ACM Transactions on Sensor Networks\\
%     TQC & ACM Transactions on Quantum Computing\\
%     TRETS & ACM Transactions on Reconfigurable Technology and Systems\\
%     TSAS & ACM Transactions on Spatial Algorithms and Systems\\
%     TSC & ACM Transactions on Social Computing\\
%     TSLP & ACM Transactions on Speech and Language Processing \\
%     TWEB & ACM Transactions on the Web\\
% \end{longtable}
%\egroup
%
% Besides the publications listed in Table~\ref{tab:pubs}, there is a
% special ``publication'' type FACMP, a forthcoming ACM publication,
% reserved for new journals which are not assigned an ISSN yet.
%
%
%\subsection{Samples}
%\label{sec:samples}
%
% There are several samples of acmart documents, that can serve as
% templates for the authors.  Among them
% |sample-sig...| are templates for conference papers, while
% |sample-acm...| are templates for journal articles and special
% materials.
%
% Here is the list of the samples:
% \begin{description}
% \item[sample-manuscript:] A proceedings paper in the manuscript format.
% \item[sample-acmsmall:] A journal paper in the |acmsmall| format.
% \item[sample-acmsmall-biblatex:] A journal paper using experimental
% \textsl{biblatex} bibliography processing.
% \item[sample-acmlarge:] A journal paper in the |acmlarge| format.
% \item[sample-acmtog:] A journal paper in the |acmtog| format.
% \item[sample-sigconf:] A proceedings paper in the standard |sigconf|
% format.
% \item[sample-sigconf-biblatex:] A proceedings paper in the |sigconf|
% format using experimental \textsl{biblatex} bibliography
% processing.
% \item[sample-sigconf-authordraft:] A proceedings paper in the
% |sigconf| format with the |authordraft| option selected.
% \item[sample-sigconf-i13n:] A proceedings paper in the
% |sigconf| format with multilanguage titles and abstract.
% \item[sample-sigconf-xelatex:] A proceedings paper in the
% |sigconf| format, Xe\LaTeX\ version.
% \item[sample-sigconf-lualatex:] A proceedings paper in the
% |sigconf| format, Lua\LaTeX\ version.
% \item[sample-sigplan:] A proceedings paper in the |sigplan| format.
% \item[sample-acmsmall-conf:] A proceedings paper in the |acmsmall|
% format for the publication in a journal using this option.
% \item[sample-acmtog-conf:] A proceedings paper in the |acmtog|
% format for the publication in a journal using this option.
% \item[sample-acmcp:] A sample of ACM Cover Page used by JDS.
% \item[sample-acmengage:] A sample of ACM Engage publication.
% \end{description}
%
%
%
%\subsection{A note about \texttt{sigchi-a} format}
%\label{sec:sigchi-a}
%
% Starting in Spring 2020 ACM retired SIGCHI Extended Abstract format
% (|sigchi-a|).  ACM will not, under any circumstances, accept
% documents in this format for publication and will not offer
% technical support to the authors who use this template.
%
% You may use this format in the |nonacm| mode only, as in
% \begin{verbatim}
% \documentclass[sigchi-a, nonacm]{acmart}
% \end{verbatim}
%
%
%
% \DescribeEnv{sidebar}%
% \DescribeEnv{marginfigure}%
% \DescribeEnv{margintable}%
% This format has large margin uses for special figures and
% tables. This package provides three environments for this with
% optional captions:
% \begin{description}
% \item[sidebar:] textual information in the margin,
% \item[marginfigure:] a figure in the margin,
% \item[margintable:] a table in the margin.
% \end{description}
%
% The environments |figure| and |table| produce figures and tables
% with the width of the text column.  The environments |figure*| and
% |table*| produce ``wide'' figures and tables, which take a large
% part of the margin.
%
% The horizontal sizes of figures are:
% \begin{enumerate}
% \item |figure|:  \cs{columnwidth},
% \item |marginfigure|: \cs{marginparwidth},
% \item |figure*|: \cs{fulltextwidth}.
% \end{enumerate}
%
%\subsection{Experiments with tagging}
%\label{sec:ug_tagged}
%
% ACM is firmly committed to produce fully tagged PDFs compliant with
% the accessibility standards.  We use the developmental version of
% tagging code by \LaTeX3 team, see the details at
% \url{https://www.latex-project.org/publications/indexbytopic/pdf/}
% and \url{https://tug.org/twg/accessibility/overview.html}.
%
% At present this work is highly experimental.  You may try the
% experiments by (1)~using the class |acmart-tagged| in the document
% class line, and (2)~adding the command
% \cs{DocumentMetadata}\oarg{options} in the preamble, see the file
% \path{sample-acmsmall-tagged.tex}.  If you do this, please \emph{do
% not ask ACM for support}.  On the other hand, bug reports at
% \url{https://github.com/borisveytsman/acmart/issues} will be
% appreciated.  
% 
%
%
% \StopEventually{
% \clearpage
% \bibliography{acmart}
% \bibliographystyle{unsrt}}
%
% \clearpage
%
%
%\section{Implementation}
%\label{sec:impl}
%
%\subsection{Identification}
%\label{sec:ident}
%
% We start with a declaration of who we are.  Most |.dtx| files put
% driver code in a separate~|.drv| driver file.  We roll this code into the
% main file and use the pseudo-guard |<gobble>| for it.
%    \begin{macrocode}
%<class>\NeedsTeXFormat{LaTeX2e}
%<*gobble>
\ProvidesFile{acmart.dtx}
%</gobble>
%<class&!tagged>\ProvidesClass{acmart}
%<tagged>\ProvidesClass{acmart-tagged}
[2025/05/30 v2.14 Typesetting articles for the Association for Computing Machinery]
%    \end{macrocode}
%
% \changes{v1.00}{2016/04/14}{First released version}
% \changes{v1.01}{2016/04/18}{Defined ACM colors}
% \changes{v1.01}{2016/04/18}{Changed hyperref colors in screen mode
% (closes \url{https://github.com/borisveytsman/acmart/issues/1})}
% \changes{v1.01}{2016/04/18}{Set headheight to 1pc for all formats
% (closes \url{https://github.com/borisveytsman/acmart/issues/5})}
% \changes{v1.02}{2016/04/21}{Documentation changes
% (closes \url{https://github.com/borisveytsman/acmart/issues/13})}
% \changes{v1.02}{2016/04/21}{Added TOPS and TSC
% (closes \url{https://github.com/borisveytsman/acmart/issues/12})}
% \changes{v1.03}{2016/04/22}{Added authorversion option
% (closes \url{https://github.com/borisveytsman/acmart/issues/9})}
% \changes{v1.03}{2016/04/22}{Added anonsuppress environment}
% \changes{v1.04}{2016/04/26}{Updated bibliography for siggraph}
% \changes{v1.05}{2016/04/27}{Patched \cs{setcitestyle} command;
% closes \url{https://github.com/borisveytsman/acmart/issues/19}}
% \changes{v1.05}{2016/04/27}{Added processing doi numbers for
% acmsiggraph and doi numbers for sigproc.bib}
% \changes{v1.08}{2016/05/13}{SIGPLAN reformatting by Matthew Fluet}
% \changes{v1.08}{2016/05/13}{Typos corrected (Tobias Pape)}
% \changes{v1.09}{2016/05/18}{Revert SIGPLAN caption rules}
% \changes{v1.11}{2016/05/27}{Customization of ACM theorem styles and
% proof environment by Matthew Fluet}
% \changes{v1.12}{2016/05/30}{Documentation updates}
% \changes{v1.14}{2016/06/09}{\cs{citestyle} updates (Matthew Fluet)}
% \changes{v1.16}{2016/07/07}{Formatting header/footer (Matthew
% Fluet)}
% \changes{v1.18}{2016/07/10}{Natbib is now the default for all
% formats}
% \changes{v1.19}{2016/07/28}{Include 'Abstract', 'Acknowledgements',
% and 'References' in PDF bookmarks (Matthew Fluet)}
% \changes{v1.20}{2016/08/06}{Bug fixes for bst}
% \changes{v1.22}{2016/09/25}{More bibliography changes for Aptara}
% \changes{v1.23}{2016/11/04}{Add PACMPL journal option}
% \changes{v1.26}{2016/12/24}{Corrected \cs{shortcite} bug}
% \changes{v1.26}{2016/12/24}{Documentation typos fixed (thanks to
% Stephen Spencer)}
% \changes{v1.30}{2017/02/04}{Bibtex style now recognizes https:// in
% doi}
% \changes{v1.31}{2017/03/04}{Documentation changes}
% \changes{v1.32}{2017/03/07}{Format siggraph is now obsolete}
% \changes{v1.32}{2017/03/07}{Added POMACS journal option}
% \changes{v1.33}{2017/03/12}{BibTeX crossref bug corrected}
% \changes{v1.33}{2017/03/18}{BibTeX comma before articleno bug
% corrected}
% \changes{v1.33}{2017/03/18}{BibTeX numpages bug corrected}
% \changes{v1.33}{2017/03/28}{Added acmart-preload-hook}
% \changes{v1.33}{2017/03/33}{Documentation updates}
% \changes{v1.35}{2017/04/23}{BibTeX bug fixed: et al.}
% \changes{v1.36}{2017/05/12}{Added the possibility to adjust number of
% author boxes per row in conference formats}
% \changes{v1.37}{2017/05/13}{Set \cs{normalparindent}; Reduce list
% indentation (Matthew Fluet)}%
% \changes{v1.38}{2017/05/13}{Increase default font size for SIGPLAN}
% \changes{v1.40}{2017/05/27}{Bibliography changes}
% \changes{v1.40}{2017/06/15}{Added package cleveref}
% \changes{v1.40}{2017/06/16}{Added new copyright version:
% licensedcagov}
% \changes{v1.41}{2017/06/25}{Added new badges}
% \changes{v1.42}{2017/07/02}{Deleted ACM badges}
% \changes{v1.44}{2017/07/30}{Added package refcount}
% \changes{v1.44}{2017/07/30}{Deleted package cleveref}
% \changes{v1.44}{2017/07/30}{Put theorem defs in a separate style}
% \changes{v1.46}{2017/08/17}{Bst file bug fixes: label width is
% calculated correctly}
% \changes{v1.46}{2017/08/25}{Added etoolbox}
% \changes{v1.46}{2017/08/29}{Restore theorem defs to class file}
% \changes{v1.47}{2017/08/31}{New journal: THRI}
% \changes{v1.48}{2017/09/09}{Typos fixed (Jamie Davis)}
% \changes{v1.48}{2017/09/16}{Code prettying (Michael D.~Adams)}
% \changes{v1.48}{2017/09/23}{Misc entries in the bibliography no
% longer produce a separate date}
% \changes{v1.48}{2017/10/01}{Initial support for Biblatex (Daniel Thomas)}
% \changes{1.48}{2017/10/14}{Bib code cleanup (Zack Weinberg)}
% \changes{1.48}{2017/12/03}{Documentation update (siggraph)}
% \changes{1.49}{2018/01/24}{New journal:  DTRAP}
% \changes{1.53}{2018/04/14}{New journals: PACMCGIT, TIOT, TDSCI}
% \changes{1.53}{2018/04/14}{Rearranged docs}
% \changes{1.54}{2018/06/17}{Moved footnote stuff before hyperref call
% (Ross Moore)}
% \changes{1.56}{2018/11/11}{Documented \cs{Description}}
% \changes{1.57}{2018/12/16}{Booktabs package is now the default}
% \changes{1.58}{2019/02/09}{Changes in samples (Enrico Gregorio)}
% \changes{1.58}{2019/03/29}{New journal: HEALTH.  TDS is renamed to
% TDSCI}
% \changes{1.60}{2019/04/22}{New option: urlbreakonhyphens}
% \changes{1.62}{2019/07/31}{New journal: TELO}
% \changes{1.63}{2019/08/04}{New journal: TQUANT}
% \changes{1.63}{2019/08/04}{New journal: FACMP}
% \changes{1.63a}{2019/08/05}{Move: TQUANT to TQC}
% \changes{1.64}{2019/08/17}{Putting abstract after \cs{maketitle} now
% causes an error}
% \changes{1.65}{2019/10/19}{New journal: DGOV}
% \changes{1.66}{2019/12/18}{ACM reference format is now mandatory for
% papers over one page;  CCS concepts and keywords are now mandatory for
% papers over two pages}
% \changes{1.66}{2019/12/18}{Authors' addresses are mandatory for
% journal articles}
% \changes{1.71}{2020/05/01}{Retired sigchi and sigchi-a}
% \changes{1.71}{2020/05/02}{Bibliography change: volume for
% @inproceedings is now in brackets together with series}
% \changes{1.71}{2020/05/02}{LuaTeX now uses the OTF versions of
% fonts}
% \changes{1.75}{2020/10/29}{Documentation update}
% \changes{1.78}{2021/05/01}{Documentation update: Word count}
% \changes{1.84}{2022/04/09}{New journals: JDS, GAMES}
% \changes{1.85}{2022/05/08}{Added CC licenses}
% \changes{1.87}{2022/08/02}{New format: |acmcp|}
% \changes{1.90a}{2023/06/11}{Changes in the sample keywords and concepts}
% \changes{1.92}{2023/09/30}{Documentation update: use of `anonymous'
% throughout} 
% \changes{v1.93}{2023/10/22}{Added PACMSE journal option}
% \changes{v2.01}{2024/01/06}{Documentation update: POMACS uses acmsmall}
% \changes{v2.03}{2024/02/04}{Added documentation about samples}
% 
% And the driver code:
%    \begin{macrocode}
%<*gobble>
\documentclass{ltxdoc}
\usepackage{array,booktabs,amsmath,graphicx,fancyvrb,tabularx, longtable}
\usepackage[tt=false, type1=true]{libertine}
\usepackage[varqu]{zi4}
\usepackage[libertine]{newtxmath}
\usepackage[tableposition=top]{caption}
\usepackage{hypdoc}
\PageIndex
\CodelineIndex
\RecordChanges
\EnableCrossrefs
\begin{document}
  \DocInput{acmart.dtx}
\end{document}
%</gobble>
%<*class>
%<!tagged>\def\@classname{acmart}
%<tagged>\def\@classname{acmart-tagged}
%    \end{macrocode}
%
%
%
%\subsection{Preload hook}
%\label{sec:preload}
%
% We preload |acmart-preload-hook|:
%    \begin{macrocode}
\InputIfFileExists{acmart-preload-hook.tex}{%
  \ClassWarning{\@classname}{%
    I am loading acmart-preload-hook.tex. You are fully responsible
    for any problems from now on.}}{}
%    \end{macrocode}
%
% \subsection{Options}
% \label{sec:options}
%
% We need |xkeyval| since some of our options may have values:
%    \begin{macrocode}
\RequirePackage{xkeyval}
%    \end{macrocode}
%
% We use |xstring| to check whether user input is valid
%    \begin{macrocode}
\RequirePackage{xstring}
%    \end{macrocode}
%
% We need |iftex| to check the engine
%    \begin{macrocode}
\RequirePackage{iftex}
%    \end{macrocode}
%
%
%
% \begin{macro}{format}
% \changes{1.85}{2022/05/08}{New format: acmengage}
% \changes{1.87}{2022/08/13}{New format: acmcp}
%   The possible formats
%    \begin{macrocode}
\define@choicekey*+{\@classname.cls}{format}[\ACM@format\ACM@format@nr]{%
  manuscript, acmsmall, acmlarge, acmtog, sigconf, siggraph,
  sigplan, sigchi, sigchi-a, acmengage, acmcp}[manuscript]{}{%
  \ClassError{\@classname}{The option format must be manuscript,
    acmsmall, acmlarge, acmtog, sigconf, siggraph,
    sigplan, sigchi or sigchi-a}}
\def\@DeclareACMFormat#1{\DeclareOptionX{#1}{\setkeys{\@classname.cls}{format=#1}}}
\@DeclareACMFormat{manuscript}
\@DeclareACMFormat{acmsmall}
\@DeclareACMFormat{acmlarge}
\@DeclareACMFormat{acmtog}
\@DeclareACMFormat{sigconf}
\@DeclareACMFormat{siggraph}
\@DeclareACMFormat{sigplan}
\@DeclareACMFormat{sigchi}
\@DeclareACMFormat{sigchi-a}
\@DeclareACMFormat{acmengage}
\@DeclareACMFormat{acmcp}
\ExecuteOptionsX{format}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@screen}
%   Whether we use screen mode
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{screen}[true]{%
  \if@ACM@screen
    \PackageInfo{\@classname}{Using screen mode}%
  \else
    \PackageInfo{\@classname}{Not using screen mode}%
  \fi}{\PackageError{\@classname}{The option screen can be either true or
    false}}
\ExecuteOptionsX{screen=false}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@urlbreakonhyphens}
% \changes{1.60}{2019/04/22}{introduced macro}
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{urlbreakonhyphens}[true]{%
  \if@ACM@urlbreakonhyphens
    \PackageInfo{\@classname}{Using breaking urls on hyphens}%
  \else
    \PackageInfo{\@classname}{Not breaking urls on hyphens}%
  \fi}{\PackageError{\@classname}{The option urlbreakonhyphens can be either true or
    false}}
\ExecuteOptionsX{urlbreakonhyphens=true}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\if@ACM@acmthm}
% \changes{v1.44}{2017/07/30}{Added macro}
% \changes{v1.46}{2017/08/29}{Modified description}
%   Whether we define theorem-like environments.
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{acmthm}[true]{%
  \if@ACM@acmthm
    \PackageInfo{\@classname}{Requiring acmthm}%
  \else
    \PackageInfo{\@classname}{Suppressing acmthm}%
  \fi}{\PackageError{\@classname}{The option acmthm can be either true or
    false}}
\ExecuteOptionsX{acmthm=true}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\if@ACM@review}
% \changes{v1.48}{2017/09/09}{Review mode now switches on folios}
%   Whether we use review mode
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{review}[true]{%
  \if@ACM@review
    \PackageInfo{\@classname}{Using review mode}%
    \AtBeginDocument{\@ACM@printfoliostrue}%
  \else
    \PackageInfo{\@classname}{Not using review mode}%
  \fi}{\PackageError{\@classname}{The option review can be either true or
    false}}
\ExecuteOptionsX{review=false}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@authorversion}
% \changes{v1.03}{2016/04/22}{Added macro}
%   Whether we use author's-version mode
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{authorversion}[true]{%
  \if@ACM@authorversion
    \PackageInfo{\@classname}{Using authorversion mode}%
  \else
    \PackageInfo{\@classname}{Not using authorversion mode}%
  \fi}{\PackageError{\@classname}{The option authorversion can be either true or
    false}}
\ExecuteOptionsX{authorversion=false}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@nonacm}
% \changes{v1.54}{2018/05/08}{Added macro}
%   Special option for non-ACM publications
%   using the ACM typesetting options.
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{nonacm}[true]{%
  \if@ACM@nonacm
    \PackageInfo{\@classname}{Using nonacm mode}%
    \AtBeginDocument{\@ACM@printacmreffalse}%
    % in 'nonacm' mode we disable the "ACM Reference Format"
    % printing by default, but this can be re-enabled by the
    % user using \settopmatter{printacmref=true}
  \else
    \PackageInfo{\@classname}{Not using nonacm mode}%
  \fi}{\PackageError{\@classname}{The option nonacm can be either true or
    false}}
\ExecuteOptionsX{nonacm=false}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@balance}
% \changes{v1.57}{2018/12/16}{Added macro}
% Whether to balance the last page
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{balance}[true]{}{%
  \PackageError{\@classname}{The option balance can be either true or
    false}}
\ExecuteOptionsX{balance}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\if@ACM@pbalance}
% \changes{v1.76}{2021/03/16}{Added macro}
% Whether to balance the last page
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{pbalance}[true]{}{%
  \PackageError{\@classname}{The option pbalance can be either true or
    false}}
\ExecuteOptionsX{pbalance=false}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\if@ACM@natbib@override}
% \changes{v1.12}{2016/05/30}{Added macro}
% \changes{v1.33}{2017/03/28}{Deleted macro}
% This macro is no longer used.
% \end{macro}
%
% \begin{macro}{\if@ACM@natbib}
%   Whether we use |natbib| mode
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{natbib}[true]{%
  \if@ACM@natbib
    \PackageInfo{\@classname}{Explicitly selecting natbib mode}%
  \else
    \PackageInfo{\@classname}{Explicitly deselecting natbib mode}%
  \fi}{\PackageError{\@classname}{The option natbib can be either true or
    false}}
\ExecuteOptionsX{natbib=true}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\if@ACM@anonymous}
%   Whether we use anonymous mode
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{anonymous}[true]{%
  \if@ACM@anonymous
    \PackageInfo{\@classname}{Using anonymous mode}%
  \else
    \PackageInfo{\@classname}{Not using anonymous mode}%
  \fi}{\PackageError{\@classname}{The option anonymous can be either true or
    false}}
\ExecuteOptionsX{anonymous=false}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\if@ACM@timestamp}
% \changes{v1.33}{2017/03/10}{Added macro (Michael D.~Adams)}
%   Whether we use timestamp mode
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{timestamp}[true]{%
  \if@ACM@timestamp
    \PackageInfo{\@classname}{Using timestamp mode}%
  \else
    \PackageInfo{\@classname}{Not using timestamp mode}%
  \fi}{\PackageError{\@classname}{The option timestamp can be either true or
    false}}
\ExecuteOptionsX{timestamp=false}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\if@ACM@authordraft}
% \changes{v1.33}{2017/03/28}{Added macro}
% \changes{v1.36}{2017/05/13}{Corrected typo, thanks to bargteil}
%   Whether we use author-draft mode
%    \begin{macrocode}
\define@boolkey+{\@classname.cls}[@ACM@]{authordraft}[true]{%
  \if@ACM@authordraft
    \PackageInfo{\@classname}{Using authordraft mode}%
    \@ACM@timestamptrue
    \@ACM@reviewtrue
  \else
    \PackageInfo{\@classname}{Not using authordraft mode}%
  \fi}{\PackageError{\@classname}{The option authordraft can be either true or
    false}}
\ExecuteOptionsX{authordraft=false}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\ACM@fontsize}
%   The font size to pass to the base class
%    \begin{macrocode}
% \changes{v1.87}{2022/08/27}{Added fontsize 8pt}
\def\ACM@fontsize{}
\DeclareOptionX{8pt}{\edef\ACM@fontsize{\CurrentOption}}
\DeclareOptionX{9pt}{\edef\ACM@fontsize{\CurrentOption}}
\DeclareOptionX{10pt}{\edef\ACM@fontsize{\CurrentOption}}
\DeclareOptionX{11pt}{\edef\ACM@fontsize{\CurrentOption}}
\DeclareOptionX{12pt}{\edef\ACM@fontsize{\CurrentOption}}
%    \end{macrocode}
%
% \end{macro}
%
% 
%
% \begin{macro}{\ACM@languages}
% \changes{v1.83}{2022/02/19}{Introduced macro}
% The languages of the document
%    \begin{macrocode}
\def\ACM@languages{}
\DeclareOptionX{language}{%
  \ifx\ACM@languages\@empty
  \gdef\ACM@languages{english}\fi
  \g@addto@macro\ACM@languages{, #1}}
%    \end{macrocode}
% 
%
% \end{macro}
%
% \changes{v1.01}{2016/04/18}{Explicitly put draft option
% (closes \url{https://github.com/borisveytsman/acmart/issues/4})}
%
%    \begin{macrocode}
\DeclareOptionX{draft}{\PassOptionsToClass{\CurrentOption}{amsart}}
\DeclareOptionX{*}{\PassOptionsToClass{\CurrentOption}{amsart}}
\ProcessOptionsX
\ClassInfo{\@classname}{Using format \ACM@format, number \ACM@format@nr}
%    \end{macrocode}
%
%
%
%\subsection{Setting switches}
%\label{sec:switches}
%
% \begin{macro}{\if@ACM@manuscript}
%   Whether we use manuscript mode
%    \begin{macrocode}
\newif\if@ACM@manuscript
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@journal}
%   There are two kinds of publications: journals and books
%    \begin{macrocode}
\newif\if@ACM@journal
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\if@ACM@journal@bibstrip}
% \changes{v1.59}{2019/04/20}{Introduced macro}
% Sometimes ACM wants a journal-like publication to have conference
% information in the bibstrip and vice versa, so we have an additional
% switch.
%    \begin{macrocode}
\newif\if@ACM@journal@bibstrip
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@journal@bibstrip@or@tog}
% \changes{v2.03}{2024/02/04}{Introduced macro}
% ACM TOG sometimes uses journal-like bibstrip even for conference
% proceedings, so we need to set it separately
%    \begin{macrocode}
\newif\if@ACM@journal@bibstrip@or@tog
%    \end{macrocode}
%
% \end{macro}
%
% 
% \begin{macro}{\if@ACM@sigchiamode}
%   The formatting of SIGCHI extended abstracts is quite unusual.  We have a
%   special switch for them.
%    \begin{macrocode}
\newif\if@ACM@sigchiamode
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@engage}
% \changes{v1.85}{2022/05/05}{Introduced macro}
% ACM Engage course materials have special formatting
%    \begin{macrocode}
\newif\if@ACM@engage
\@ACM@engagefalse
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\if@ACM@acmcp}
% \changes{v1.87}{2022/08/12}{Introduced macro}
% ACM cover page formatting
%    \begin{macrocode}
\newif\if@ACM@acmcp
\@ACM@acmcpfalse
%    \end{macrocode}
% 
% \end{macro}
%
% 
%
% Setting up switches
%    \begin{macrocode}
\ifnum\ACM@format@nr=5\relax % siggraph
\ClassWarning{\@classname}{%
  The format siggraph is now obsolete.\MessageBreak
  I am switching to sigconf.}
  \setkeys{\@classname.cls}{format=sigconf}
\fi
\ifnum\ACM@format@nr=7\relax % sigchi
\ClassWarning{\@classname}{%
  The format sigchi is now obsolete.\MessageBreak
  I am switching to sigconf.}
  \setkeys{\@classname.cls}{format=sigconf}
\fi
\ifnum\ACM@format@nr=8\relax % sigchi
\ClassWarning{\@classname}{%
  ACM SIGCHI has retired the SIGCHI-A template\MessageBreak
  effective immediately. ACM is keeping this template\MessageBreak
  option available to authors who are working on legacy\MessageBreak
  documents only. ACM will not, under any circumstances,\MessageBreak
  accept documents in this format for publication and\MessageBreak
  will not offer technical support to the authors who use\MessageBreak
  this template.\MessageBreak
  ACM SIGCHI is directing Conference leaders and\MessageBreak
  authors to publish their articles using the SIGCONF\MessageBreak
  template call.}
\fi
\ifnum\ACM@format@nr=0\relax
  \@ACM@manuscripttrue
\else
  \@ACM@manuscriptfalse
\fi
\@ACM@sigchiamodefalse
\ifcase\ACM@format@nr
\relax % manuscript
  \@ACM@journaltrue
\or % acmsmall
  \@ACM@journaltrue
\or % acmlarge
  \@ACM@journaltrue
\or % acmtog
  \@ACM@journaltrue
  \@ACM@journal@bibstrip@or@togtrue
\or % sigconf
  \@ACM@journalfalse
\or % siggraph
  \@ACM@journalfalse
 \or % sigplan
  \@ACM@journalfalse
 \or % sigchi
  \@ACM@journalfalse
\or % sigchi-a
  \@ACM@journalfalse
  \@ACM@sigchiamodetrue
\or % acmengage
  \@ACM@journalfalse
  \@ACM@engagetrue
\or % acmcp
  \@ACM@journaltrue
  \@ACM@acmcptrue
  \AtBeginDocument{\@ACM@printacmreffalse}%
\fi
\if@ACM@journal
  \@ACM@journal@bibstriptrue
  \@ACM@journal@bibstrip@or@togtrue
\else
 \@ACM@journal@bibstripfalse
\fi
%    \end{macrocode}
%
%
%
%\subsection{Loading the base class and package}
%\label{sec:loading}
%
% \changes{v1.13}{2016/06/06}{Increased font size for ACM Large}
% \changes{v1.38}{2017/05/13}{Increase default font size for SIGPLAN}
% \changes{v1.87}{2022/08/27}{Set font sizes for |acmengage| and |acmcp|}
%
%<*tagged>
% \changes{v2.09}{2022/08/25}{Saved the old definitions (Ulrike's
% code}
% Before we load the base class we want to save the original
% definitions of sectioning commands since |acmart| redefines them,
% and we need the vanilla versions.
% \begin{macro}{\@startsection@kernel}
% \changes{v2.00}{2023/11/19}{Added macro}
%    \begin{macrocode}
\let\@startsection@kernel\@startsection
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\@xsect@kernel}
% \changes{v2.00}{2023/11/19}{Added macro}
%    \begin{macrocode}
\let\@xsect@kernel\@xsect
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\@sect@kernel}
% \changes{v2.00}{2023/11/19}{Added macro}
%    \begin{macrocode}
\let\@sect@kernel\@sect
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\@ssect@kernel}
% \changes{v2.00}{2023/11/19}{Added macro}
%    \begin{macrocode}
\let\@ssect@kernel\@ssect
%    \end{macrocode}
% \end{macro}
% 
%</tagged>
%
%
% At this point we either have \cs{ACM@fontsize} or use defaults
%    \begin{macrocode}
\ifx\ACM@fontsize\@empty
  \ifcase\ACM@format@nr
  \relax % manuscript
    \def\ACM@fontsize{9pt}%
  \or % acmsmall
    \def\ACM@fontsize{10pt}%
  \or % acmlarge
    \def\ACM@fontsize{10pt}%
  \or % acmtog
    \def\ACM@fontsize{9pt}%
  \or % sigconf
    \def\ACM@fontsize{9pt}%
  \or % siggraph
    \def\ACM@fontsize{9pt}%
   \or % sigplan
    \def\ACM@fontsize{10pt}%
   \or % sigchi
    \def\ACM@fontsize{9pt}%
  \or % sigchi-a
    \def\ACM@fontsize{10pt}%
  \or % acmengage
    \def\ACM@fontsize{10pt}%
  \or % acmcp  
    \def\ACM@fontsize{9pt}%
  \fi
\fi
\ClassInfo{\@classname}{Using fontsize \ACM@fontsize}
\LoadClass[\ACM@fontsize, reqno]{amsart}
\RequirePackage{microtype}
%    \end{macrocode}
%
%
% We need |etoolbox| for delayed code
%    \begin{macrocode}
\RequirePackage{etoolbox}
%    \end{macrocode}
%
% Booktabs is now the default
%    \begin{macrocode}
\RequirePackage{booktabs}
%    \end{macrocode}
%
%
% We need |totpages| to calculate the number of pages and
% |refcount| to use that number
%    \begin{macrocode}
\RequirePackage{refcount}
\RequirePackage{totpages}
%    \end{macrocode}
%
% The \cs{collect@body} macro in |amsmath| is defined using \cs{def}.  We load
% |environ| to access the \cs{long} version of this command
%    \begin{macrocode}
\RequirePackage{environ}
%    \end{macrocode}
%
% We use |setspace| for double spacing
%    \begin{macrocode}
\if@ACM@manuscript
\RequirePackage{setspace}
\onehalfspacing
\fi
%    \end{macrocode}
%
% \changes{v1.40}{2017/06/05}{Added `textcase' package}
% \changes{v2.12}{2024/12/28}{Removed `textcase' package}
% We no longer need |textcase| for better upcasing.
%    \begin{macrocode}
% \RequirePackage{textcase}
%    \end{macrocode}
%
%
% \changes{v1.87}{2022/08/14}{Added `framed' package for acmcp}
% \changes{v1.89}{2022/12/25}{Added `zref-savepos' package for acmcp}
%    \begin{macrocode}
\if@ACM@acmcp
\RequirePackage{framed}
\RequirePackage{zref-savepos, zref-user}
\fi
%    \end{macrocode}
% 
%
% \begin{macro}{\@ACM@acmcp@delta}
% \changes{v1.89}{2022/12/25}{Added dimen}
% We need to store the dimen to store the insert length of amcp box
%    \begin{macrocode}
\newdimen\@ACM@acmcp@delta
\@ACM@acmcp@delta=0pt\relax
%    \end{macrocode}
% \end{macro}
%
%\subsection{Citations}
% \changes{v1.19}{2016/07/28}{Include 'References' in PDF bookmarks
% (Matthew Fluet)}
% \changes{v1.14}{2016/06/09}{Patched \cs{citestyle}}
% \changes{v1.55}{2016/08/012}{Typo corrected (Zack Weinberg)}
% We patch \cs{setcitestyle} to allow, for example,
% \cs{setcitestyle}|{sort}| and \cs{setcitestyle}|{nosort}|.  We patch
% \cs{citestyle} to warn about undefined citation styles.
%    \begin{macrocode}
\if@ACM@natbib
  \RequirePackage{natbib}
  \renewcommand{\bibsection}{%
     \section*{\refname}%
     \phantomsection\addcontentsline{toc}{section}{\refname}%
  }
  \renewcommand{\bibfont}{\bibliofont}
  \renewcommand\setcitestyle[1]{
  \@for\@tempa:=#1\do
  {\def\@tempb{round}\ifx\@tempa\@tempb
     \renewcommand\NAT@open{(}\renewcommand\NAT@close{)}\fi
   \def\@tempb{square}\ifx\@tempa\@tempb
     \renewcommand\NAT@open{[}\renewcommand\NAT@close{]}\fi
   \def\@tempb{angle}\ifx\@tempa\@tempb
     \renewcommand\NAT@open{$<$}\renewcommand\NAT@close{$>$}\fi
   \def\@tempb{curly}\ifx\@tempa\@tempb
     \renewcommand\NAT@open{\{}\renewcommand\NAT@close{\}}\fi
   \def\@tempb{semicolon}\ifx\@tempa\@tempb
     \renewcommand\NAT@sep{;}\fi
   \def\@tempb{colon}\ifx\@tempa\@tempb
     \renewcommand\NAT@sep{;}\fi
   \def\@tempb{comma}\ifx\@tempa\@tempb
     \renewcommand\NAT@sep{,}\fi
   \def\@tempb{authoryear}\ifx\@tempa\@tempb
     \NAT@numbersfalse\fi
   \def\@tempb{numbers}\ifx\@tempa\@tempb
     \NAT@numberstrue\NAT@superfalse\fi
   \def\@tempb{super}\ifx\@tempa\@tempb
     \NAT@numberstrue\NAT@supertrue\fi
   \def\@tempb{nobibstyle}\ifx\@tempa\@tempb
     \let\bibstyle=\@gobble\fi
   \def\@tempb{bibstyle}\ifx\@tempa\@tempb
     \let\bibstyle=\@citestyle\fi
   \def\@tempb{sort}\ifx\@tempa\@tempb
     \def\NAT@sort{\@ne}\fi
   \def\@tempb{nosort}\ifx\@tempa\@tempb
     \def\NAT@sort{\z@}\fi
   \def\@tempb{compress}\ifx\@tempa\@tempb
     \def\NAT@cmprs{\@ne}\fi
   \def\@tempb{nocompress}\ifx\@tempa\@tempb
     \def\NAT@cmprs{\z@}\fi
   \def\@tempb{sort&compress}\ifx\@tempa\@tempb
     \def\NAT@sort{\@ne}\def\NAT@cmprs{\@ne}\fi
   \def\@tempb{mcite}\ifx\@tempa\@tempb
     \let\NAT@merge\@ne\fi
   \def\@tempb{merge}\ifx\@tempa\@tempb
     \@ifnum{\NAT@merge<\tw@}{\let\NAT@merge\tw@}{}\fi
   \def\@tempb{elide}\ifx\@tempa\@tempb
     \@ifnum{\NAT@merge<\thr@@}{\let\NAT@merge\thr@@}{}\fi
   \def\@tempb{longnamesfirst}\ifx\@tempa\@tempb
     \NAT@longnamestrue\fi
   \def\@tempb{nonamebreak}\ifx\@tempa\@tempb
     \def\NAT@nmfmt#1{\mbox{\NAT@up#1}}\fi
   \expandafter\NAT@find@eq\@tempa=\relax\@nil
   \if\@tempc\relax\else
     \expandafter\NAT@rem@eq\@tempc
     \def\@tempb{open}\ifx\@tempa\@tempb
      \xdef\NAT@open{\@tempc}\fi
     \def\@tempb{close}\ifx\@tempa\@tempb
      \xdef\NAT@close{\@tempc}\fi
     \def\@tempb{aysep}\ifx\@tempa\@tempb
      \xdef\NAT@aysep{\@tempc}\fi
     \def\@tempb{yysep}\ifx\@tempa\@tempb
      \xdef\NAT@yrsep{\@tempc}\fi
     \def\@tempb{notesep}\ifx\@tempa\@tempb
      \xdef\NAT@cmt{\@tempc}\fi
     \def\@tempb{citesep}\ifx\@tempa\@tempb
      \xdef\NAT@sep{\@tempc}\fi
   \fi
  }%
  \NAT@@setcites
  }
  \renewcommand\citestyle[1]{%
    \ifcsname bibstyle@#1\endcsname%
    \csname bibstyle@#1\endcsname\let\bibstyle\@gobble%
    \else%
    \@latex@error{Undefined `#1' citestyle}%
    \fi
  }%
\fi
%    \end{macrocode}
%
% \begin{macro}{\bibstyle@acmauthoryear}
% \changes{v1.13}{2016/06/06}{Added macro}
% \changes{v1.14}{2016/06/09}{Moved def of \cs{bibstyle@acmauthoryear}
%   before use}
% \changes{v1.35}{2017/04/13}{Square brackets for author-year style}
%   The default author-year format:
%    \begin{macrocode}
\newcommand{\bibstyle@acmauthoryear}{%
  \setcitestyle{%
    authoryear,%
    open={[},close={]},citesep={;},%
    aysep={},yysep={,},%
    notesep={, }}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\bibstyle@acmnumeric}
% \changes{v1.13}{2016/06/06}{Added macro}
% \changes{v1.14}{2016/06/09}{Moved def of \cs{bibstyle@numeric}
%   before use}
%   The default numeric format:
%    \begin{macrocode}
\newcommand{\bibstyle@acmnumeric}{%
  \setcitestyle{%
    numbers,sort&compress,%
    open={[},close={]},citesep={,},%
    notesep={, }}}
%    \end{macrocode}
%
% \end{macro}
%
% \changes{v1.28}{2017/01/07}{Corrected option natbib behavior}
% The default is numeric:
%    \begin{macrocode}
\if@ACM@natbib
\citestyle{acmnumeric}
\fi
%    \end{macrocode}
%
%
%\subsection{Internationalization}
%\label{sec:i13n}
%
%
% 
% \changes{v1.83}{2022/02/19}{Use babel for multilanguage papers}
%    \begin{macrocode}
\if@ACM@journal
  \renewcommand\keywordsname{Additional Key Words and Phrases}%
\else
  \renewcommand\keywordsname{Keywords}%
\fi 
\if@ACM@engage
   \renewcommand\abstractname{Synopsis}%
\fi
\ifx\ACM@languages\@empty
\else
  \RequirePackage[\ACM@languages]{babel}%
  \addto\captionsenglish{%
      \if@ACM@journal
        \renewcommand\keywordsname{Additional Key Words and Phrases}%
      \else
        \renewcommand\keywordsname{Keywords}%
      \fi 
      \renewcommand\acksname{Acknowledgements}%
      \if@ACM@engage
         \renewcommand\abstractname{Synopsis}%
      \fi
  }%
  \addto\captionsfrench{%
      \if@ACM@journal
        \renewcommand\keywordsname{Mots Clés et Phrases Supplémentaires}%
      \else
        \renewcommand\keywordsname{Mots clés}%
      \fi 
      \renewcommand\acksname{Remerciements}%
  }%
  \addto\captionsgerman{%
      \if@ACM@journal
        \renewcommand\keywordsname{Zusätzliche Schlagwörter und Phrasen}%
      \else
        \renewcommand\keywordsname{Schlagwörter}%
      \fi 
      \renewcommand\acksname{Danksagungen}%
  }%
  \addto\captionsspanish{%
      \if@ACM@journal
        \renewcommand\keywordsname{Palabras y Frases Claves Adicionales}%
      \else
        \renewcommand\keywordsname{Palabras claves}%
      \fi 
      \renewcommand\acksname{Expresiones de gratitud}%
  }%
\fi
%    \end{macrocode}
%
% \begin{macro}{\ACM@lang@check}
% \changes{v1.83}{2022/02/20}{Added macro}
% Some commands should not be used if the document is monlingual
%    \begin{macrocode}
\newcommand\ACM@lang@check[1]{%
  \ifx\ACM@languages\@empty\relax
  \ClassError{\@classname}{%
    Command \string#1 \MessageBreak is used in monlingual document}{%
    You used a command (\string#1) \MessageBreak
    that does not have a meaning \MessageBreak
    unless are languages are defined. \MessageBreak
    Please choose the languages in \string\documentclass
    \MessageBreak
    (e.g. \string\documentclass[languages={french, english}]{acmart}), 
    \MessageBreak
    or delete the command.}%
  \fi}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\@translatedtitle}
% \changes{v1.83}{2022/02/20}{Added macro}
%    \begin{macrocode}
\def\@translatedtitle{}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\translatedtitle}
% \changes{v1.83}{2022/02/20}{Added macro}
% The title of the paper in a different language
%    \begin{macrocode}
\newcommand\translatedtitle[2]{\ACM@lang@check{\translatedtitle}%
\g@addto@macro\@translatedtitle{\par\foreignlanguage{#1}{#2}}}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\@translatedsubtitle}
% \changes{v1.83}{2022/02/20}{Added macro}
%    \begin{macrocode}
\def\@translatedsubtitle{}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\translatedsubtitle}
% \changes{v1.83}{2022/02/20}{Added macro}
% The subtitle of the paper in a different language
%    \begin{macrocode}
\newcommand\translatedsubtitle[2]{\ACM@lang@check{\translatedsubtitle}%
\g@addto@macro\@translatedsubtitle{\par\foreignlanguage{#1}{#2}}}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\@translatedkeywords}
% \changes{v1.83}{2022/02/20}{Added macro}
%    \begin{macrocode}
\def\@translatedkeywords{}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\translatedkeywords}
% \changes{v1.83}{2022/02/20}{Added macro}
% Add keywords to the paper in the language specified
%    \begin{macrocode}
\newcommand\translatedkeywords[2]{\ACM@lang@check{\translatedkeywords}%
\g@addto@macro\@translatedkeywords{\@mktranslatedkeywords{#1}{#2}}}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\@translatedabstracts}
% \changes{v1.83}{2022/02/20}{Added macro}
% The container for translated abstracts.
%    \begin{macrocode}
\def\@translatedabstracts{}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{translatedabstract}
% \changes{v1.83}{2022/02/20}{Added macro}
%   We save translated abstracts into \cs{@translatedabstracts} 
%    \begin{macrocode}
\newenvironment{translatedabstract}[1]{\Collect@Body
  \@savetranslatedabstract\@mktranslatedabstract{#1}}{}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@savetranslatedabstract}
%   And saving the abstract
%    \begin{macrocode}
\long\def\@savetranslatedabstract#1{\if@ACM@maketitle@typeset
  \ClassError{\@classname}{Abstract must be defined before maketitle
    command. Please move it!}\fi
  \ACM@lang@check{translatedabstract}%
  \g@addto@macro\@translatedabstracts{\bgroup#1\egroup}}
%    \end{macrocode}
%
% \end{macro}
%
%
%\subsection{Sectioning}
%\label{sec:sectioning}
%
%
%
% \begin{macro}{\@startsection}
% \changes{v1.31}{2017/03/04}{Added \cs{tochangmeasure}}
% Before we call |hyperref|, we redefine \cs{startsection} commands to
% their \LaTeX\ defaults since the |amsart| ones are too AMS-specific.
% We need to do this early since we want |hyperref| to have a chance
% to redefine them again:
%    \begin{macrocode}
%</class>
%<*tagged>
\let\@startsection\@startsection@kernel
\let\@sect\@sect@kernel
\let\@ssect\@ssect@kernel
\let\@xsect\@xsect@kernel
%</tagged>
%<*class&!tagged>
\def\@startsection#1#2#3#4#5#6{%
  \if@noskipsec \leavevmode \fi
  \par
  \@tempskipa #4\relax
  \@afterindenttrue
  \ifdim \@tempskipa <\z@
    \@tempskipa -\@tempskipa \@afterindentfalse
  \fi
  \if@nobreak
    \everypar{}%
  \else
    \addpenalty\@secpenalty\addvspace\@tempskipa
  \fi
  \@ifstar
    {\@ssect{#3}{#4}{#5}{#6}}%
    {\@dblarg{\@sect{#1}{#2}{#3}{#4}{#5}{#6}}}}
\def\@sect#1#2#3#4#5#6[#7]#8{%
  \edef\@toclevel{\ifnum#2=\@m 0\else\number#2\fi}%
  \ifnum #2>\c@secnumdepth
    \let\@svsec\@empty
  \else
    \refstepcounter{#1}%
    \protected@edef\@svsec{\@seccntformat{#1}\relax}%
  \fi
  \@tempskipa #5\relax
  \ifdim \@tempskipa>\z@
    \begingroup
      #6{%
        \@hangfrom{\hskip #3\relax\@svsec}%
          \interlinepenalty \@M #8\@@par}%
    \endgroup
    \csname #1mark\endcsname{#7}%
    \ifnum #2>\c@secnumdepth \else
        \@tochangmeasure{\csname the#1\endcsname}%
    \fi
    \addcontentsline{toc}{#1}{%
      \ifnum #2>\c@secnumdepth \else
        \protect\numberline{\csname the#1\endcsname}%
      \fi
      #7}%
  \else
    \def\@svsechd{%
      #6{\hskip #3\relax
      \@svsec #8}%
      \csname #1mark\endcsname{#7}%
      \ifnum #2>\c@secnumdepth \else
        \@tochangmeasure{\csname the#1\endcsname\space}%
      \fi
      \addcontentsline{toc}{#1}{%
        \ifnum #2>\c@secnumdepth \else
          \protect\numberline{\csname the#1\endcsname}%
        \fi
        #7}}%
  \fi
  \@xsect{#5}}
\def\@xsect#1{%
  \@tempskipa #1\relax
  \ifdim \@tempskipa>\z@
    \par \nobreak
    \vskip \@tempskipa
    \@afterheading
  \else
    \@nobreakfalse
    \global\@noskipsectrue
    \everypar{%
      \if@noskipsec
        \global\@noskipsecfalse
       {\setbox\z@\lastbox}%
        \clubpenalty\@M
        \begingroup \@svsechd \endgroup
        \unskip
        \@tempskipa #1\relax
        \hskip -\@tempskipa
      \else
        \clubpenalty \@clubpenalty
        \everypar{}%
      \fi}%
  \fi
  \ignorespaces}
\def\@ssect#1#2#3#4#5{%
  \@tempskipa #3\relax
  \ifdim \@tempskipa>\z@
    \begingroup
      #4{%
        \@hangfrom{\hskip #1}%
          \interlinepenalty \@M #5\@@par}%
    \endgroup
  \else
    \def\@svsechd{#4{\hskip #1\relax #5}}%
  \fi
  \@xsect{#3}}
%</class&!tagged>
%<*class>
\def\@seccntformat#1{\csname the#1\endcsname\quad}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@starttoc}
% \changes{v1.31}{2017/03/04}{Redefined macro}
% \changes{v1.43}{2017/07/09}{Added \cs{makeatletter}}
%   The |amsart| package redefines \cs{startsection}.  Here we redefine
%   it again to make the table of contents work.
%    \begin{macrocode}
\def\@starttoc#1#2{\begingroup\makeatletter
  \setTrue{#1}%
  \par\removelastskip\vskip\z@skip
  \@startsection{section}\@M\z@{\linespacing\@plus\linespacing}%
    {.5\linespacing}{\centering\contentsnamefont}{#2}%
%<tagged>\@starttoc@cfgpoint@before{#1}%NEW<<<<<<<<<< (name will change)
  \@input{\jobname.#1}%
%<tagged>\@starttoc@cfgpoint@after{#1}%NEW<<<<<<<<<<<<  (name will change)
  \if@filesw
    \@xp\newwrite\csname tf@#1\endcsname
    \immediate\@xp\openout\csname tf@#1\endcsname \jobname.#1\relax
  \fi
  \global\@nobreakfalse \endgroup
  \addvspace{32\p@\@plus14\p@}%
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\l@subsection}
% \changes{v1.40}{2017/05/27}{Redefined macro}
%   Section spacing is more generous than for |amsart|
%    \begin{macrocode}
\def\l@section{\@tocline{1}{0pt}{1pc}{2pc}{}}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\l@subsection}
% \changes{v1.31}{2017/03/04}{Redefined macro}
%   The spacing in |amsart| is too large
%    \begin{macrocode}
\def\l@subsection{\@tocline{2}{0pt}{1pc}{3pc}{}}
%    \end{macrocode}
%
% \end{macro}
% \begin{macro}{\l@subsubsection}
% \changes{v1.31}{2017/03/04}{Redefined macro}
% \changes{v1.71}{2020/04/30}{Bug fixed (thanks to Andrew Black)}
%   The spacing in |amsart| is too large
%    \begin{macrocode}
\def\l@subsubsection{\@tocline{3}{0pt}{1pc}{5pc}{}}
%    \end{macrocode}
%
% \end{macro}
%
% We need to define foonote-related stuff before the call to hyperref
% (Ross Moore)
% \begin{macro}{\@makefntext}
%   We do not use indentation for footnotes
%    \begin{macrocode}
\def\@makefntext{\noindent\@makefnmark}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@footnotetext}
%   In |sigchi-a| mode our footnotes are in the margin!
%    \begin{macrocode}
\if@ACM@sigchiamode
\long\def\@footnotetext#1{\marginpar{%
    \reset@font\small
    \interlinepenalty\interfootnotelinepenalty
    \protected@edef\@currentlabel{%
       \csname p@footnote\endcsname\@thefnmark
    }%
    \color@begingroup
      \@makefntext{%
        \rule\z@\footnotesep\ignorespaces#1\@finalstrut\strutbox}%
    \color@endgroup}}%
\fi
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mpfootnotetext}
% \changes{v1.13}{2016/06/06}{Made minipage footnotes centered}
%   We want the footnotes in minipages centered:
%    \begin{macrocode}
\long\def\@mpfootnotetext#1{%
  \global\setbox\@mpfootins\vbox{%
    \unvbox\@mpfootins
    \reset@font\footnotesize
    \hsize\columnwidth
    \@parboxrestore
    \protected@edef\@currentlabel
         {\csname p@mpfootnote\endcsname\@thefnmark}%
    \color@begingroup\centering
      \@makefntext{%
        \rule\z@\footnotesep\ignorespaces#1\@finalstrut\strutbox}%
    \color@endgroup}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@makefnmark}
% \changes{v1.17}{2016/067/09}{Redefined}
%   AMS classes use a buggy definition of \cs{makefnmark}.  We revert
%   to the standard one.
%    \begin{macrocode}
\def\@makefnmark{\hbox{\@textsuperscript{\normalfont\@thefnmark}}}
%    \end{macrocode}
%
% \end{macro}
%
%
%\subsection{Hyperxmp and hyperref}
%\label{sec:hyper}
%
%
%
%
%
% Adding |hyperref|
% \changes{v1.28}{2017/01/07}{Got rid of warnings in pdf keywords}
% \changes{v1.46}{2017/08/25}{Delayed hypersetup since journal options
% may change screen mode}
% \changes{v1.55}{2018/10/20}{Now we use purple color for links}
% \changes{v1.58}{2019/26/01}{Suppressed \cs{addtocounter} in pdf
% subject}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% \changes{v1.76}{2021/02/21}{Moved hyperxmp before hyperref, see
% \url{https://github.com/borisveytsman/acmart/issues/425}}
% \changes{1.92}{2023/10/14}{Moved hyperxmp again due to kernel update}
% \changes{2.04}{2024/03/17}{Explicitly load ifdraft since hyperxmp
% tries to load it inside a group}
% \changes{2.04}{2024/03/31}{Deleted loading ifdraft due to the new
% release of hyperxmp}
%    \begin{macrocode}
\let\@footnotemark@nolink\@footnotemark
\let\@footnotetext@nolink\@footnotetext
\RequirePackage[bookmarksnumbered,unicode]{hyperref}
\RequirePackage{hyperxmp}
\pdfstringdefDisableCommands{%
  \def\addtocounter#1#2{}%
  \def\unskip{}%
  \def\textbullet{- }%
  \def\textrightarrow{ -> }%
  \def\footnotemark{}%
}
\urlstyle{rm}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
\or % acmlarge
\or % acmtog
\or % sigconf
\or % siggraph
\or % sigplan
  \urlstyle{sf}
\or % sigchi
\or % sigchi-a
  \urlstyle{sf}
\or % acmengage
\or % acmcp  
\fi
\AtEndPreamble{%
  \if@ACM@urlbreakonhyphens
    \def\do@url@hyp{\do\-}%
  \fi
  \if@ACM@screen
    \hypersetup{colorlinks,
      linkcolor=ACMPurple,
      citecolor=ACMPurple,
      urlcolor=ACMDarkBlue,
      filecolor=ACMDarkBlue}
    \else
    \hypersetup{hidelinks}
  \fi
  \hypersetup{pdflang={en},
    pdfdisplaydoctitle}}
%    \end{macrocode}
%
%
% Bibliography mangling.
% \changes{v1.33}{2017/03/23}{Moved \cs{citename} definition for
% non-natbib bibliography, so a package may redefine it}
% \changes{v1.48}{2017/09/23}{\cs{citeyear} no longer behaves like
% \cs{citeyearpar}}
%    \begin{macrocode}
\if@ACM@natbib
  \let\citeN\cite
  \let\cite\citep
  \let\citeANP\citeauthor
  \let\citeNN\citeyearpar
  \let\citeyearNP\citeyear
  \let\citeNP\citealt
  \DeclareRobustCommand\citeA
     {\begingroup\NAT@swafalse
       \let\NAT@ctype\@ne\NAT@partrue\NAT@fullfalse\NAT@open\NAT@citetp}%
  \providecommand\newblock{}%
\else
  \AtBeginDocument{%
    \let\shortcite\cite%
    \providecommand\citename[1]{#1}}
\fi
\newcommand\shortcite[2][]{%
  \ifNAT@numbers\cite[#1]{#2}\else\citeyearpar[#1]{#2}\fi}
%    \end{macrocode}
%
%
% \begin{macro}{\bibliographystyle}
% \changes{v1.13}{2016/06/06}{Redefined macro}
%   The |amsart| package redefines \cs{bibliographystyle} since it
%   prefers the AMS bibliography style.  We turn it back to the
%   \LaTeX\ definition:
%    \begin{macrocode}
\def\bibliographystyle#1{%
  \ifx\@begindocumenthook\@undefined\else
    \expandafter\AtBeginDocument
  \fi
    {\if@filesw
       \immediate\write\@auxout{\string\bibstyle{#1}}%
     \fi}}
%    \end{macrocode}
%
% \end{macro}
%
%
%\subsection{Other packages}
%\label{sec:packages}
%
%
%
% Graphics and color.
% \changes{1.48}{2017/10/22}{Added prologue option to xcolor}
%    \begin{macrocode}
\RequirePackage{graphicx}
\RequirePackage[prologue]{xcolor}
%    \end{macrocode}
%
% We define ACM colors according to~\cite{ACMIdentityStandards}:
%    \begin{macrocode}
\definecolor[named]{ACMBlue}{cmyk}{1,0.1,0,0.1}
\definecolor[named]{ACMYellow}{cmyk}{0,0.16,1,0}
\definecolor[named]{ACMOrange}{cmyk}{0,0.42,1,0.01}
\definecolor[named]{ACMRed}{cmyk}{0,0.90,0.86,0}
\definecolor[named]{ACMLightBlue}{cmyk}{0.49,0.01,0,0}
\definecolor[named]{ACMGreen}{cmyk}{0.20,0,1,0.19}
\definecolor[named]{ACMPurple}{cmyk}{0.55,1,0,0.15}
\definecolor[named]{ACMDarkBlue}{cmyk}{1,0.58,0,0.21}
%    \end{macrocode}
%
%
% Author-draft mode or sigchi-a mode
%    \begin{macrocode}
\if@ACM@authordraft
  \RequirePackage{draftwatermark}
  \SetWatermarkFontSize{0.5in}
  \SetWatermarkColor[gray]{.9}
  \SetWatermarkText{\parbox{12em}{\centering
      Unpublished working draft.\\
      Not for distribution.}}
\else
  \if@ACM@sigchiamode
    \if@ACM@nonacm\else
      \RequirePackage{draftwatermark}
      \SetWatermarkFontSize{0.5in}
      \SetWatermarkColor[gray]{.9}
      \SetWatermarkText{\parbox{12em}{\centering
          Legacy document. \\
          Not for publication in an ACM venue}}
    \fi
  \fi
\fi
%    \end{macrocode}
%
%
%\subsection{Paper size and paragraphing}
%\label{sec:paper}
%
% \changes{v1.17}{2016/07/07}{Slightly decreased margins for sigs}
% \changes{v1.29}{2017/01/22}{Increased head to 13pt}
% \changes{v1.40}{2017/07/15}{Added heightrounded to geometry}
% \changes{v1.56}{2018/10/16}{Make two-column layouts flush (Philip Quinn)}
% We use |geometry| for dimensions.  Note that the present margins do not
% depend on the font size option---we might need to change this.
% See \url{https://github.com/borisveytsman/acmart/issues/5#issuecomment-272881329}.
%    \begin{macrocode}
\RequirePackage{geometry}
\ifcase\ACM@format@nr
\relax % manuscript
   \geometry{letterpaper,head=13pt,
   marginparwidth=6pc,heightrounded}%
\or % acmsmall
   \geometry{twoside=true,
     includeheadfoot, head=13pt, foot=2pc,
     paperwidth=6.75in, paperheight=10in,
     top=58pt, bottom=44pt, inner=46pt, outer=46pt,
     marginparwidth=2pc,heightrounded
   }%
\or % acmlarge
   \geometry{twoside=true, head=13pt, foot=2pc,
     paperwidth=8.5in, paperheight=11in,
     includeheadfoot,
     top=78pt, bottom=114pt, inner=81pt, outer=81pt,
     marginparwidth=4pc,heightrounded
     }%
\or % acmtog
   \geometry{twoside=true, head=13pt, foot=2pc,
     paperwidth=8.5in, paperheight=11in,
     includeheadfoot, columnsep=24pt,
     top=52pt, bottom=75pt, inner=52pt, outer=52pt,
     marginparwidth=2pc,heightrounded
     }%
\or % sigconf
   \geometry{twoside=true, head=13pt,
     paperwidth=8.5in, paperheight=11in,
     includeheadfoot, columnsep=2pc,
     top=57pt, bottom=73pt, inner=54pt, outer=54pt,
     marginparwidth=2pc,heightrounded
     }%
\or % siggraph
   \geometry{twoside=true, head=13pt,
     paperwidth=8.5in, paperheight=11in,
     includeheadfoot, columnsep=2pc,
     top=57pt, bottom=73pt, inner=54pt, outer=54pt,
     marginparwidth=2pc,heightrounded
     }%
\or % sigplan
   \geometry{twoside=true, head=13pt,
     paperwidth=8.5in, paperheight=11in,
     includeheadfoot=false, columnsep=2pc,
     top=1in, bottom=1in, inner=0.75in, outer=0.75in,
     marginparwidth=2pc,heightrounded
     }%
\or % sigchi
   \geometry{twoside=true, head=13pt,
     paperwidth=8.5in, paperheight=11in,
     includeheadfoot, columnsep=2pc,
     top=66pt, bottom=73pt, inner=54pt, outer=54pt,
     marginparwidth=2pc,heightrounded
     }%
\or % sigchi-a
   \geometry{twoside=false, head=13pt,
     paperwidth=11in, paperheight=8.5in,
     includeheadfoot, marginparsep=72pt,
     marginparwidth=170pt, columnsep=20pt,
     top=72pt, bottom=72pt, left=314pt, right=72pt
     }%
     \@mparswitchfalse
     \reversemarginpar
\or % acmengage
   \geometry{twoside=true, head=13pt,
     paperwidth=8.5in, paperheight=11in,
     includeheadfoot, columnsep=2pc,
     top=57pt, bottom=73pt, inner=54pt, outer=54pt,
     marginparwidth=2pc,heightrounded
     }%
\or % acmcp
   \geometry{twoside=true,
     includeheadfoot, head=13pt, foot=2pc,
     paperwidth=6.75in, paperheight=10in,
     top=58pt, bottom=44pt, inner=46pt, outer=46pt,
     marginparwidth=2pc,heightrounded
   }%
\fi
%    \end{macrocode}
%
%
% \begin{macro}{\parindent}
% \begin{macro}{\parskip}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% Paragraphing
%    \begin{macrocode}
\setlength\parindent{10\p@}
\setlength\parskip{\z@}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
\or % acmlarge
\or % acmtog
  \setlength\parindent{9\p@}%
\or % sigconf
\or % siggraph
\or % sigplan
\or % sigchi
\or % sigchi-a
\or % acmengage
\or % acmcp
\fi
%    \end{macrocode}
%
% \end{macro}
% \end{macro}
%
% \begin{macro}{\normalparindent}
% \changes{v1.37}{2017/05/13}{Set \cs{normalparindent} (Matthew Fluet)}%
%   The |amsart| package defines the \cs{normalparindent} length and
%   initializes it to 12pt (the value of \cs{parindent} in |amsart|).  It
%   is later used to set the \cs{listparindent} length in the |quotation|
%   environment and the \cs{parindent} length in the \cs{@footnotetext}
%   command.  We set \cs{normalparindent} to the value of \cs{parindent}
%   as selected by |acmart| for consistent paragraph indents.
%    \begin{macrocode}
\setlength\normalparindent{\parindent}
%    \end{macrocode}
%
% \end{macro}
%
% Footnotes require some consideration.  We have several layers of
% footnotes:  frontmatter footnotes, ``regular'' footnotes and the
% special insert for the bibstrip.  In the old ACM classes, the bibstrip
% was a \cs{@float}.  The problem with floats is that they tend to, well,
% float---and we want the guarantee they stay.
%
% We use |manyfoot| for layered footnotes instead.
%
% \begin{macro}{\copyrightpermissionfootnoterule}
% \changes{v1.12}{2016/05/30}{Added macro}
%   This is the footnote rule that separates the bibstrip from the rest of
%   the paper.  It is a full width rule.
%    \begin{macrocode}
\def\copyrightpermissionfootnoterule{\kern-3\p@
  \hrule \@width \columnwidth \kern 2.6\p@}
%    \end{macrocode}
% \end{macro}
%
%    \begin{macrocode}
\RequirePackage{manyfoot}
\SelectFootnoteRule[2]{copyrightpermission}
\DeclareNewFootnote{authorsaddresses}
\SelectFootnoteRule[2]{copyrightpermission}
\DeclareNewFootnote{copyrightpermission}
%    \end{macrocode}
%
%
% \begin{macro}{\footnoterule}
% \changes{v1.12}{2016/05/30}{Made shorter}
%   Tschichold's rules:
%    \begin{macrocode}
\def\footnoterule{\kern-3\p@
  \hrule \@width 4pc \kern 2.6\p@}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\endminipage}
%   We do not use footnote rules in minipages
%    \begin{macrocode}
\def\endminipage{%
    \par
    \unskip
    \ifvoid\@mpfootins\else
      \vskip\skip\@mpfootins
      \normalcolor
      \unvbox\@mpfootins
    \fi
    \@minipagefalse
  \color@endgroup
  \egroup
  \expandafter\@iiiparbox\@mpargs{\unvbox\@tempboxa}}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@textbottom}
% \changes{v1.31}{2017/03/04}{Redefined}
%   Add some stretch according to David Carlisle's advice at
%   \url{http://tex.stackexchange.com/a/62318/5522}
%    \begin{macrocode}
\def\@textbottom{\vskip \z@ \@plus 1pt}
\let\@texttop\relax
%    \end{macrocode}
%
% \end{macro}
%
% In two-column layouts, force both columns to be the same height by inserting
% extra internal vertical space to fill out the page.
%    \begin{macrocode}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
\or % acmlarge
\or % acmtog
  \flushbottom
\or % sigconf
  \flushbottom
\or % siggraph
  \flushbottom
\or % sigplan
  \flushbottom
\or % sigchi
  \flushbottom
\or % sigchi-a
\or % acmengage
  \flushbottom
\or % acmcp
  \flushbottom
\fi
%    \end{macrocode}
%
%\subsection{Fonts}
%\label{sec:fonts}
%
% \changes{v1.12}{2016/05/30}{Added graceful behavior when libertine
% fonts are absent}
% \changes{v1.33}{2017/03/29}{Added cmap and glyphtounicode}
% \changes{v1.40}{2017/05/27}{Added Ross Moore code for glyphtounicode}
%
% Somehow PDFTeX and XeTeX require different incantations to make a PDF
% compliant with the current Acrobat bugs.  Xpdf is much better.
%
% The code below is by Ross Moore.
%    \begin{macrocode}
\ifPDFTeX
\input{glyphtounicode}
\pdfglyphtounicode{f_f}{FB00}
\pdfglyphtounicode{f_f_i}{FB03}
\pdfglyphtounicode{f_f_l}{FB04}
\pdfglyphtounicode{f_i}{FB01}
\pdfglyphtounicode{t_t}{0074 0074}
\pdfglyphtounicode{f_t}{0066 0074}
\pdfglyphtounicode{T_h}{0054 0068}
\pdfgentounicode=1
\RequirePackage{cmap}
\fi
%    \end{macrocode}
%
%
% \begin{macro}{\if@ACM@newfonts}
% \changes{v1.12}{2016/05/30}{Added macro}%
%   Whether we load the new fonts
%    \begin{macrocode}
\newif\if@ACM@newfonts
\@ACM@newfontstrue
\IfFileExists{libertine.sty}{}{\ClassWarning{\@classname}{You do not
    have the libertine package installed.  Please upgrade your
    TeX}\@ACM@newfontsfalse}
\IfFileExists{zi4.sty}{}{\ClassWarning{\@classname}{You do not
    have the inconsolata (zi4.sty) package installed.  Please upgrade your
    TeX}\@ACM@newfontsfalse}
\IfFileExists{newtxmath.sty}{}{\ClassWarning{\@classname}{You do not
    have the newtxmath package installed.  Please upgrade your
    TeX}\@ACM@newfontsfalse}
%    \end{macrocode}
%
% \end{macro}
%
% \changes{v1.30}{2017/02/15}{Switched to T1: looks like libertine has
% problems with \cs{l} in OT1}%
% \changes{v1.33}{2017/03/12}{Switched to Type~1 fonts for libertine
% even if OTF-capable engine is used (Kai Mindermann)}
% \changes{v1.51}{2018/04/05}{XeTeX now uses OTF fonts (Joachim
% Breitner)}
% \changes{v1.54}{2018/07/16}{XeTeX now uses T1 encoding}
% \changes{v1.56}{2018/11/11}{Looks like XeTeX requires explicit
% monotype font declaration}
% \changes{v1.74}{2020/10/25}{Changed the order of font loading}%
% We use Libertine throughout.
% \changes{v2.12}{2024/12/29}{Use unicode-math and libertinus for Unicode engines}%
% \changes{v2.13}{2025/01/11}{Added mathcal in unicode mode}%
% We use Libertine throughout.
%    \begin{macrocode}
\if@ACM@newfonts
  % Note that the order in which packages are loaded matters,
  % and the correct order depends on the LaTeX engine used.
  % See https://github.com/borisveytsman/acmart/issues/402
  % and https://github.com/borisveytsman/acmart/issues/410
  \ifxetex
    \RequirePackage{unicode-math}
    \setmathfont[Scale=MatchUppercase]{LibertinusMath-Regular.otf}
    \setmathfont{latinmodern-math.otf}[range={cal}]
    \RequirePackage[tt=false]{libertine}
    \setmonofont[StylisticSet=3]{inconsolata}
  \else
    \ifluatex
      \RequirePackage{unicode-math}
      \setmathfont[Scale=MatchUppercase]{LibertinusMath-Regular.otf}
      \setmathfont{latinmodern-math.otf}[range={cal}]
      \RequirePackage[tt=false]{libertine}
      \setmonofont[StylisticSet=3]{inconsolata}
    \else
       \RequirePackage[T1]{fontenc}
       \RequirePackage[tt=false, type1=true]{libertine}
       \RequirePackage[varqu]{zi4}
       \RequirePackage[libertine]{newtxmath}
    \fi
  \fi
\fi
%    \end{macrocode}
%
% \begin{macro}{\liningnums}
% \changes{v1.46}{2017/08/28}{Workaround for compatibility with fontspec}
% Libertine defines \cs{liningnums}, which makes |fontspec| unhappy.
% While we do not use |fontspec|, some users do.
%    \begin{macrocode}
\let\liningnums\@undefined
\AtEndPreamble{%
  \DeclareTextFontCommand{\liningnums}{\libertineLF}}
%    \end{macrocode}
%
% \end{macro}
%
%
% The SIGCHI extended abstracts are sans serif:
%    \begin{macrocode}
\if@ACM@sigchiamode
  \renewcommand{\familydefault}{\sfdefault}
\fi
%    \end{macrocode}
%
%
%
%\subsection{Image descriptions}
%\label{sec:description}
%
% \begin{macro}{\if@Description@present}
% \changes{v1.56}{2018/11/11}{Added macro}
% A local switch: whether there is a description in the given figure
%    \begin{macrocode}
\newif\if@Description@present
\@Description@presenttrue
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\if@undescribed@images}
% \changes{v1.56}{2018/11/11}{Added macro}
% A global switch whether some images were \emph{not} described
%    \begin{macrocode}
\newif\if@undescribed@images
\@undescribed@imagesfalse
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\Description}
% \changes{v1.56}{2018/11/11}{Added macro}
% The Description macro right now just sets switches
%    \begin{macrocode}
\newcommand\Description[2][]{\global\@Description@presenttrue\ignorespaces}
%    \end{macrocode}
% \end{macro}
%
% The global warning
% \changes{v2.13}{2025/01/11}{New warnings about lack of descriptions}
%    \begin{macrocode}
\AtEndDocument{\if@undescribed@images
  \ClassWarningNoLine{\@classname}{Some images may lack
    descriptions.\MessageBreak
  ACM is committed to complying with the upcoming US ADA
  https://accessiblyapp.com/accessibility-compliance/ada/ and European
  Accessibility Act
  (EAA) https://accessiblyapp.com/accessibility-compliance/eaa/
  regulations by actively working to ensure our publications and
  application services are accessible to individuals with
  disabilities, adhering to the WCAG guidelines to provide a seamless
  experience for all users, and regularly reviewing our accessibility
  practices to maintain compliance with evolving standards. \MessageBreak
  To this end, we strongly encourage our authors to provide
  alternative text and captions for images and multimedia content. It
  is also important to optimize color contrast for the visually
  impaired. Taking these important steps when creating your papers
  will ensure that the widest possible audience can ingest your
  work.}\fi} 
%    \end{macrocode}
%
% Hooks for figures and tables.  We also patch |algorithm| and
% |algorithm*| in case somebody uses them.
% \changes{v1.66}{2019/11/23}{Patching algorithm and algorithm*
% to prevent spurious space warnings}
% \changes{v1.67}{2020/01/11}{Patching lstlisting and lstlisting*
% to prevent spurious space warnings}
% \changes{v1.73}{2020/08/23}{Patching minted and minted*
% to prevent spurious space warnings}
%    \begin{macrocode}
\AtBeginEnvironment{figure}{\@Description@presentfalse
  \let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{figure*}{\@Description@presentfalse
  \let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtEndEnvironment{figure}{\if@Description@present\else
  \global\@undescribed@imagestrue
  \ClassWarning{\@classname}{A possible image without description}\fi}
\AtEndEnvironment{figure*}{\if@Description@present\else
  \global\@undescribed@imagestrue
  \ClassWarning{\@classname}{A possible image without description}\fi}
\AtBeginEnvironment{table}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{table*}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{algorithm}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{algorithm*}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{lstlisting}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{lstlisting*}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{minted}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{minted*}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{listing}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{listing*}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{lstinputlisting}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}
\AtBeginEnvironment{lstinputlisting*}{\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig}

%    \end{macrocode}
%
%
%\subsection{Floats}
%\label{sec:floats}
%
% \changes{v1.55}{2018/10/20}{Table captions for SIGCHI now use the
% same font size as figure captions (Luis Leiva)}
% We use the |caption| package
%    \begin{macrocode}
\RequirePackage{caption, float}
\captionsetup[table]{position=top}
\if@ACM@journal
  \captionsetup{labelfont={sf, small},
    textfont={sf, small}, margin=\z@}
  \captionsetup[figure]{name={Fig.}}
\else
  \captionsetup{labelfont={bf},
    textfont={bf}, labelsep=colon, margin=\z@}
  \ifcase\ACM@format@nr
  \relax % manuscript
  \or % acmsmall
  \or % acmlarge
  \or % acmtog
  \or % sigconf
  \or % siggraph
    \captionsetup{textfont={it}}
  \or % sigplan
    \captionsetup{labelfont={bf},
      textfont={normalfont}, labelsep=period, margin=\z@}
  \or % sigchi
    \captionsetup[figure]{labelfont={bf, small},
      textfont={bf, small}}
    \captionsetup[table]{labelfont={bf, small},
      textfont={bf, small}}
  \or % sigchi-a
    \captionsetup[figure]{labelfont={bf, small},
      textfont={bf, small}}
    \captionsetup[table]{labelfont={bf, small},
      textfont={bf, small}}
  \or % acmengage
  \or % acmcp
  \fi
\fi
%    \end{macrocode}
%
% \begin{macro}{sidebar}
%   The |sidebar| environment:
%    \begin{macrocode}
\newfloat{sidebar}{}{sbar}
\floatname{sidebar}{Sidebar}
\renewenvironment{sidebar}{\Collect@Body\@sidebar}{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@sidebar}
% \changes{v1.58}{2019/01/23}{Deleted spurious \cs{vspace} warning}
%   The processing of the saved text
%    \begin{macrocode}
\long\def\@sidebar#1{\bgroup\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig\captionsetup{type=sidebar}%
  \marginpar{\small#1}\egroup}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{marginfigure}
%   The |marginfigure| environment:
%    \begin{macrocode}
\newenvironment{marginfigure}{\Collect@Body\@marginfigure}{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@marginfigure}
% \changes{v1.12}{2016/05/30}{Now centering by default}
% \changes{v1.56}{2018/11/11}{Added checking for description}
% \changes{v1.58}{2019/01/23}{Deleted spurious \cs{vspace} warning}
%   The processing of the saved text
%    \begin{macrocode}
\long\def\@marginfigure#1{\bgroup
  \let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig
  \captionsetup{type=figure}%
  \marginpar{\@Description@presentfalse\centering
    \small#1\if@Description@present\else
      \global\@undescribed@imagestrue
      \ClassWarning{\@classname}{A possible image without description}
      \fi}%
  \egroup}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{margintable}
%   The |margintable| environment:
%    \begin{macrocode}
\newenvironment{margintable}{\Collect@Body\@margintable}{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@margintable}
% \changes{v1.12}{2016/05/30}{Now centering by default}
% \changes{v1.58}{2019/01/23}{Deleted spurious \cs{vspace} warning}
%   The processing of the saved text
%    \begin{macrocode}
\long\def\@margintable#1{\bgroup\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig\captionsetup{type=table}%
  \marginpar{\centering\small#1}\egroup}
%    \end{macrocode}
%
% \end{macro}
%
%
% SIGCHI extended abstracts provide an interesting possibility to push
% into the margin.  Here we use |figure*| and |table*| for this.
% \begin{macro}{\fulltextwidth}
%   We define the width of the boxes as
%    \begin{macrocode}
\newdimen\fulltextwidth
\fulltextwidth=\dimexpr(\textwidth+\marginparwidth+\marginparsep)
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@dblfloat}
% \changes{v1.58}{2019/01/23}{Deleted spurious \cs{vspace} warning}
%   We redefine the double-float command.  First, we make the size
%   bigger.  Second, our default position is going to be |tp| (to give
%   marginalia a chance)
%    \begin{macrocode}
\if@ACM@sigchiamode
\def\@dblfloat{\bgroup\let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig\columnwidth=\fulltextwidth
  \let\@endfloatbox\@endwidefloatbox
  \def\@fpsadddefault{\def\@fps{tp}}%
  \@float}
\fi
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\end@dblfloat}
% And the end.  Just adding a \cs{bgroup}.
%    \begin{macrocode}
\if@ACM@sigchiamode
\def\end@dblfloat{%
    \end@float\egroup}
\fi
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@endwidefloatbox}
%   This is the end of a wide box---we basically move everything
%   to the left
%    \begin{macrocode}
\def\@endwidefloatbox{%
  \par\vskip\z@skip
  \@minipagefalse
  \outer@nobreak
  \egroup
  \color@endbox
  \global\setbox\@currbox=\vbox{\moveleft
    \dimexpr(\fulltextwidth-\textwidth)\box\@currbox}%
  \wd\@currbox=\textwidth
}
%    \end{macrocode}
%
% \end{macro}
%
%
%\subsection{Lists}
%\label{sec:lists}
%
%    \begin{macrocode}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
\or % acmlarge
\or % acmtog
\or % sigconf
\or % siggraph
\or % sigplan
\def\labelenumi{\theenumi.}
\def\labelenumii{\theenumii.}
\def\labelenumiii{\theenumiii.}
\def\labelenumiv{\theenumiv.}
\or % sigchi
\or % sigchi-a
\or % acmengage
\or % acmcp
\fi
%    \end{macrocode}
%
%
% \changes{v1.37}{2017/05/13}{Reduce list indentation (Matthew Fluet)}%
% The AMS uses generous margins for lists.  Note that |amsart| defines
% \cs{leftmargin} values for list levels at the beginning of the
% document, so we must redefine them in the same manner.  Also, note that
% |amsart| redefines the |enumerate| and |itemize| environments with a
% \cs{makelabel} command that uses \cs{llap}, so the \cs{labelwidth}
% value is~(effectively) irrelevant; nonetheless, we follow |amsart|
% and set \cs{labelwidth} to \cs{leftmargin} minus \cs{labelsep}.
%    \begin{macrocode}
\newdimen\@ACM@labelwidth
\AtBeginDocument{%
  \setlength\labelsep{4pt}
  \setlength{\@ACM@labelwidth}{6.5pt}

  %% First-level list: when beginning after the first line of an
  %% indented paragraph or ending before an indented paragraph, labels
  %% should not hang to the left of the preceding/following text.
  \setlength\leftmargini{\z@}
  \addtolength\leftmargini{\parindent}
  \addtolength\leftmargini{2\labelsep}
  \addtolength\leftmargini{\@ACM@labelwidth}

  %% Second-level and higher lists.
  \setlength\leftmarginii{\z@}
  \addtolength\leftmarginii{0.5\labelsep}
  \addtolength\leftmarginii{\@ACM@labelwidth}
  \setlength\leftmarginiii{\leftmarginii}
  \setlength\leftmarginiv{\leftmarginiii}
  \setlength\leftmarginv{\leftmarginiv}
  \setlength\leftmarginvi{\leftmarginv}
  \@listi}
\newskip\listisep
\listisep\smallskipamount
\def\@listI{\leftmargin\leftmargini
  \labelwidth\leftmargini \advance\labelwidth-\labelsep
  \listparindent\z@
  \topsep\listisep}
\let\@listi\@listI
\def\@listii{\leftmargin\leftmarginii
  \labelwidth\leftmarginii \advance\labelwidth-\labelsep
  \topsep\z@skip}
\def\@listiii{\leftmargin\leftmarginiii
  \labelwidth\leftmarginiii \advance\labelwidth-\labelsep}
\def\@listiv{\leftmargin\leftmarginiv
  \labelwidth\leftmarginiv \advance\labelwidth-\labelsep}
\def\@listv{\leftmargin\leftmarginv
  \labelwidth\leftmarginv \advance\labelwidth-\labelsep}
\def\@listvi{\leftmargin\leftmarginvi
  \labelwidth\leftmarginvi \advance\labelwidth-\labelsep}
%    \end{macrocode}
%
%
% \begin{macro}{\descriptionlabel}
% \changes{v1.37}{2017/05/13}{Reduce list indentation (Matthew Fluet)}%
% \changes{v1.12}{2016/05/30}{Redefined}
%   We do not use a colon by default like |amsart| does:
%    \begin{macrocode}
\renewcommand{\descriptionlabel}[1]{\upshape\bfseries #1}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\description}
% \changes{v1.37}{2017/05/13}{Reduce list indentation (Matthew Fluet)}%
% \changes{v1.17}{2016/07/07}{Decreased indent}
%   Make the |description| environment indentation consistent with that of
%   the |itemize| and |enumerate| environments.
%    \begin{macrocode}
\renewenvironment{description}{\list{}{%
    \labelwidth\@ACM@labelwidth
    \let\makelabel\descriptionlabel}%
}{
  \endlist
}
\let\enddescription=\endlist % for efficiency
%    \end{macrocode}
%
% \end{macro}
%
%
%\subsection{Top-matter data}
%\label{sec:top_matter_data}
%
% \begin{macro}{\if@ACM@maketitle@typeset}
% \changes{v1.64}{2019/08/17}{Introduced macro}
% The switch to check whether \cs{maketitle} is already typeset.  It
% is initally false, and setting top matter when it is true is wrong.
%    \begin{macrocode}
\newif\if@ACM@maketitle@typeset
\@ACM@maketitle@typesetfalse
%    \end{macrocode}
%
% \end{macro}
%
% \changes{v1.24}{2016/11/16}{Add IMWUT journal option}
% \changes{v1.25}{2016/12/03}{Updated PACMPL}
% \changes{v1.30}{2017/02/15}{Updated IMWUT and PACMPL}
% \changes{v1.36}{2017/05/13}{Added PACMHCI journal options}
% \changes{v1.46}{2017/08/25}{PACM now set screen to true}
% \changes{v1.53}{2018/04/14}{Added PACMCGIT, TIOT, TDSCI}
% \changes{v1.58}{2019/03/29}{Added HEALTH.  TDSCI is renamed to TDS}
% \changes{1.70}{2020/02/22}{Name change for TDS}
% \changes{1.79}{2021/08/29}{Updated indormation for TAP, TCPS, TEAC}
% \changes{1.80}{2021/09/24}{Added DLT and FAC}
% \changes{1.82}{2022/02/05}{Updated abbreviations and codes for
% several journals}
% \changes{1.84}{2022/04/09}{New journals: JDS, GAMES}
% \changes{1.85}{2022/05/08}{New journal: JRC}
% \changes{1.87}{2022/10/18}{New journals: ACMJCSS, JATS, TORS}
% \changes{1.88}{2022/10/19}{Added ISSN for GAMES}
% \changes{1.88}{2022/10/23}{Added ISSN for ACMJCSS}
% \changes{1.88}{2022/10/24}{Added PACMNET}
% \changes{1.89}{2023/03/25}{Added PACMMOD}
% \changes{1.89}{2023/03/25}{Added TOPML}
% \changes{1.90}{2023/03/30}{Added TOPML eISSN}
% \changes{2.05}{2024/04/04}{New data for TELO}
% \changes{2.06}{2024/04/13}{Added eISSN for a number of journals}
% \changes{2.07}{2024/04/17}{Another change for TELO}
%
% We use the |xkeyval| interface to define journal titles and the relevant
% information
%    \begin{macrocode}
\define@choicekey*+{ACM}{acmJournal}[\@journalCode\@journalCode@nr]{%
  ACMJCSS,%
  CIE,%
  CSUR,%
  DGOV,%
  DLT,%
  DTRAP,%
  FAC,%
  GAMES,%
  HEALTH,%
  IMWUT,%
  JACM,%
  JATS,%
  JDIQ,%
  JDS,%
  JEA,%
  JERIC,%
  JETC,%
  JOCCH,%
  JRC,%
  PACMCGIT,%
  PACMHCI,%
  PACMMOD,%
  PACMNET,%
  PACMPL,%
  PACMSE,%
  POMACS,%
  TAAS,%
  TACCESS,%
  TACO,%
  TALG,%
  TALLIP,%
  TAP,%
  TCPS,%
  TDS,%
  TEAC,%
  TECS,%
  TELO,%
  THRI,%
  TIIS,%
  TIOT,%
  TISSEC,%
  TIST,%
  TKDD,%
  TMIS,%
  TOCE,%
  TOCHI,%
  TOCL,%
  TOCS,%
  TOCT,%
  TODAES,%
  TODS,%
  TOG,%
  TOIS,%
  TOIT,%
  TOMACS,%
  TOMM,%
  TOMPECS,%
  TOMS,%
  TOPC,%
  TOPLAS,%
  TOPML,%
  TOPS,%
  TORS,%
  TOS,%
  TOSEM,%
  TOSN,%
  TQC,%
  TRETS,%
  TSAS,%
  TSC,%
  TSLP,%
  TWEB,%
  FACMP%
}{%
\ifcase\@journalCode@nr
\relax % ACMJCSS
  \def\@journalName{ACM Journal on Computing and Sustainable Societies}%
  \def\@journalNameShort{ACM J. Comput. Sustain. Soc.}%
  \def\@permissionCodeOne{2834-5533}%
  \def\@permissionCodeTwo{2834-5533}%
\or % CIE
  \def\@journalName{ACM Computers in Entertainment}%
  \def\@journalNameShort{ACM Comput. Entertain.}%
  \def\@permissionCodeOne{1544-3574}%
\or % CSUR
  \def\@journalName{ACM Computing Surveys}%
  \def\@journalNameShort{ACM Comput. Surv.}%
  \def\@permissionCodeOne{0360-0300}%
  \def\@permissionCodeTwo{1557-7341}%
\or % DGOV
  \def\@journalName{Digital Government: Research and Practice}%
  \def\@journalNameShort{Digit. Gov. Res. Pract.}%
  \def\@permissionCodeOne{2639-0175}%
  \def\@permissionCodeTwo{2639-0175}%
\or % DLT
  \def\@journalName{Distributed Ledger Technologies: Research and Practice}%
  \def\@journalNameShort{Distrib. Ledger Technol.}%
  \def\@permissionCodeOne{2769-6472}%
  \def\@permissionCodeTwo{2769-6480}%
\or % DTRAP
  \def\@journalName{Digital Threats: Research and Practice}%
  \def\@journalNameShort{Digit. Threat. Res. Pract.}%
  \def\@permissionCodeOne{2576-5337}%
  \def\@permissionCodeTwo{2576-5337}%
\or % FAC
  \def\@journalName{Formal Aspects of Computing}%
  \def\@journalNameShort{Form. Asp. Comput.}%
  \def\@permissionCodeOne{0934-5043}%
  \def\@permissionCodeTwo{1433-299X}%
\or % GAMES
  \def\@journalName{ACM Games: Research and Practice}%
  \def\@journalNameShort{ACM Games}%
  \def\@permissionCodeOne{2832-5516}%
  \def\@permissionCodeTwo{2832-5516}%
\or % HEALTH
  \def\@journalName{ACM Transactions on Computing for Healthcare}%
  \def\@journalNameShort{ACM Trans. Comput. Healthcare}%
  \def\@permissionCodeOne{2637-8051}%
  \def\@permissionCodeTwo{2637-8051}%
\or % IMWUT
  \def\@journalName{Proceedings of the ACM on Interactive, Mobile,
    Wearable and Ubiquitous Technologies}%
  \def\@journalNameShort{Proc. ACM Interact. Mob. Wearable Ubiquitous Technol.}%
  \def\@permissionCodeOne{2474-9567}%
  \def\@permissionCodeTwo{2474-9567}%
  \@ACM@screentrue
  \PackageInfo{\@classname}{Using screen mode due to \@journalCode}%
\or % JACM
  \def\@journalName{Journal of the ACM}%
  \def\@journalNameShort{J. ACM}%
  \def\@permissionCodeOne{0004-5411}%
  \def\@permissionCodeTwo{1557-735X}%
\or % JATS
  \def\@journalName{Journal on Autonomous Transportation Systems}%
  \def\@journalNameShort{ACM J. Auton. Transport. Syst.}%
  \def\@permissionCodeOne{2833-0528}%
  \def\@permissionCodeTwo{2833-0528}%
\or % JDIQ
  \def\@journalName{ACM Journal of Data and Information Quality}%
  \def\@journalNameShort{ACM J. Data Inform. Quality}%
  \def\@permissionCodeOne{1936-1955}%
  \def\@permissionCodeTwo{1936-1963}%
\or % JDS
  \def\@journalName{ACM/IMS Journal of Data Science}%
  \def\@journalNameShort{ACM/IMS J. Data Sci.}%
  \def\@permissionCodeOne{2831-3194}%
  \def\@permissionCodeTwo{2831-3194}%
\or % JEA
  \def\@journalName{ACM Journal of Experimental Algorithmics}%
  \def\@journalNameShort{ACM J. Exp. Algor.}%
  \def\@permissionCodeOne{1084-6654}%
  \def\@permissionCodeTwo{1084-6654}%
\or % JERIC
  \def\@journalName{ACM Journal of Educational Resources in Computing}%
  \def\@journalNameShort{ACM J. Edu. Resources in Comput.}%
  \def\@permissionCodeOne{1073-0516}%
\or % JETC
  \def\@journalName{ACM Journal on Emerging Technologies in Computing Systems}%
  \def\@journalNameShort{ACM J. Emerg. Technol. Comput. Syst.}%
  \def\@permissionCodeOne{1550-4832}%
  \def\@permissionCodeTwo{1550-4840}%
\or % JOCCH
  \def\@journalName{ACM Journal on Computing and Cultural Heritage}%
  \def\@journalNameShort{ACM J. Comput. Cult. Herit.}%
  \def\@permissionCodeOne{1556-4673}%
  \def\@permissionCodeTwo{1556-4711}%
\or % JRC
  \def\@journalName{ACM Journal on Responsible Computing}%
  \def\@journalNameShort{ACM J. Responsib. Comput.}%
  \def\@permissionCodeOne{2832-0565}%
  \def\@permissionCodeTwo{2832-0565}%
\or % PACMCGIT
  \def\@journalName{Proceedings of the ACM on Computer Graphics and Interactive Techniques}%
  \def\@journalNameShort{Proc. ACM Comput. Graph. Interact. Tech.}%
  \def\@permissionCodeOne{2577-6193}%
  \def\@permissionCodeTwo{2577-6193}%
  \@ACM@screentrue
  \PackageInfo{\@classname}{Using screen mode due to \@journalCode}%
\or % PACMHCI
  \def\@journalName{Proceedings of the ACM on Human-Computer Interaction}%
  \def\@journalNameShort{Proc. ACM Hum.-Comput. Interact.}%
  \def\@permissionCodeOne{2573-0142}%
  \def\@permissionCodeTwo{2573-0142}%
  \@ACM@screentrue
  \PackageInfo{\@classname}{Using screen mode due to \@journalCode}%
\or % PACMMOD
  \def\@journalName{Proceedings of the ACM on Management of Data}%
  \def\@journalNameShort{Proc. ACM Manag. Data}%
  \def\@permissionCodeOne{2836-6573}%
  \def\@permissionCodeTwo{2836-6573}%
\or % PACMNET
  \def\@journalName{Proceedings of the ACM on Networkng}%
  \def\@journalNameShort{Proc. ACM Netw.}%
  \def\@permissionCodeOne{2834-5509}%
  \def\@permissionCodeTwo{2834-5509}%
\or % PACMPL
  \def\@journalName{Proceedings of the ACM on Programming Languages}%
  \def\@journalNameShort{Proc. ACM Program. Lang.}%
  \def\@permissionCodeOne{2475-1421}%
  \def\@permissionCodeTwo{2475-1421}%
  \@ACM@screentrue
  \PackageInfo{\@classname}{Using screen mode due to \@journalCode}%
\or % PACMSE
  \def\@journalName{Proceedings of the ACM on Software Engineering}%
  \def\@journalNameShort{Proc. ACM Softw. Eng.}%
  \def\@permissionCodeOne{2994-970X}%
  \def\@permissionCodeTwo{2994-970X}%
  \@ACM@screentrue
  \PackageInfo{\@classname}{Using screen mode due to \@journalCode}%
\or % POMACS
  \def\@journalName{Proceedings of the ACM on Measurement and Analysis of Computing Systems}%
  \def\@journalNameShort{Proc. ACM Meas. Anal. Comput. Syst.}%
  \def\@permissionCodeOne{2476-1249}%
  \def\@permissionCodeTwo{2476-1249}%
  \@ACM@screentrue
  \PackageInfo{\@classname}{Using screen mode due to \@journalCode}%
\or % TAAS
  \def\@journalName{ACM Transactions on Autonomous and Adaptive Systems}%
  \def\@journalNameShort{ACM Trans. Autonom. Adapt. Syst.}%
  \def\@permissionCodeOne{1556-4665}%
  \def\@permissionCodeTwo{1556-4703}%
\or % TACCESS
  \def\@journalName{ACM Transactions on Accessible Computing}%
  \def\@journalNameShort{ACM Trans. Access. Comput.}%
  \def\@permissionCodeOne{1936-7228}%
  \def\@permissionCodeTwo{1936-7236}%
\or % TACO
  \def\@journalName{ACM Transactions on Architecture and Code Optimization}%
  \def\@journalNameShort{ACM Trans. Arch. Code Optim.}%
  \def\@permissionCodeOne{1544-3566}%
  \def\@permissionCodeTwo{1544-3973}%
\or % TALG
  \def\@journalName{ACM Transactions on Algorithms}%
  \def\@journalNameShort{ACM Trans. Algor.}%
  \def\@permissionCodeOne{1549-6325}%
  \def\@permissionCodeTwo{1549-6333}%
\or % TALLIP
  \def\@journalName{ACM Transactions on Asian and Low-Resource Language Information Processing}%
  \def\@journalNameShort{ACM Trans. Asian Low-Resour. Lang. Inf. Process.}%
  \def\@permissionCodeOne{2375-4699}%
  \def\@permissionCodeTwo{2375-4702}%
\or % TAP
  \def\@journalName{ACM Transactions on Applied Perception}%
  \def\@journalNameShort{ACM Trans. Appl. Percept.}%
  \def\@permissionCodeOne{1544-3558}%
  \def\@permissionCodeTwo{1544-3965}%
\or % TCPS
  \def\@journalName{ACM Transactions on Cyber-Physical Systems}%
  \def\@journalNameShort{ACM Trans. Cyber-Phys. Syst.}%
  \def\@permissionCodeOne{2378-962X}%
  \def\@permissionCodeTwo{2378-9638}%
\or % TDS
  \def\@journalName{ACM/IMS Transactions on Data Science}%
  \def\@journalNameShort{ACM/IMS Trans. Data Sci.}%
  \def\@permissionCodeOne{2577-3224}%
\or % TEAC
  \def\@journalName{ACM Transactions on Economics and Computation}%
  \def\@journalNameShort{ACM Trans. Econ. Comput.}%
  \def\@permissionCodeOne{2167-8375}%
  \def\@permissionCodeTwo{2167-8383}%
\or % TECS
  \def\@journalName{ACM Transactions on Embedded Computing Systems}%
  \def\@journalNameShort{ACM Trans. Embedd. Comput. Syst.}%
  \def\@permissionCodeOne{1539-9087}%
  \def\@permissionCodeTwo{1558-3465}%
\or % TELO
  \def\@journalName{ACM Transactions on Evolutionary Learning and Optimization}%
  \def\@journalNameShort{ACM Trans. Evol. Learn. Optim.}%
  \def\@permissionCodeOne{2688-299X}%
  \def\@permissionCodeTwo{2688-3007}%
\or % THRI
  \def\@journalName{ACM Transactions on Human-Robot Interaction}%
  \def\@journalNameShort{ACM Trans. Hum.-Robot Interact.}%
  \def\@permissionCodeOne{2573-9522}%
  \def\@permissionCodeTwo{2573-9522}%
\or % TIIS
  \def\@journalName{ACM Transactions on Interactive Intelligent Systems}%
  \def\@journalNameShort{ACM Trans. Interact. Intell. Syst.}%
  \def\@permissionCodeOne{2160-6455}%
  \def\@permissionCodeTwo{2160-6463}%
\or % TIOT
  \def\@journalName{ACM Transactions on Internet of Things}%
  \def\@journalNameShort{ACM Trans. Internet Things}%
  \def\@permissionCodeOne{2577-6207}%
  \def\@permissionCodeTwo{2577-6207}%
\or % TISSEC
  \def\@journalName{ACM Transactions on Information and System Security}%
  \def\@journalNameShort{ACM Trans. Info. Syst. Sec.}%
  \def\@permissionCodeOne{1094-9224}%
\or % TIST
  \def\@journalName{ACM Transactions on Intelligent Systems and Technology}%
  \def\@journalNameShort{ACM Trans. Intell. Syst. Technol.}%
  \def\@permissionCodeOne{2157-6904}%
  \def\@permissionCodeTwo{2157-6912}%
\or % TKDD
  \def\@journalName{ACM Transactions on Knowledge Discovery from Data}%
  \def\@journalNameShort{ACM Trans. Knowl. Discov. Data.}%
  \def\@permissionCodeOne{1556-4681}%
  \def\@permissionCodeTwo{1556-472X}%
\or % TMIS
  \def\@journalName{ACM Transactions on Management Information Systems}%
  \def\@journalNameShort{ACM Trans. Manag. Inform. Syst.}%
  \def\@permissionCodeOne{2158-656X}%
  \def\@permissionCodeTwo{2158-6578}%
\or % TOCE
  \def\@journalName{ACM Transactions on Computing Education}%
  \def\@journalNameShort{ACM Trans. Comput. Educ.}%
  \def\@permissionCodeOne{1946-6226}%
  \def\@permissionCodeTwo{1946-6226}%
\or % TOCHI
  \def\@journalName{ACM Transactions on Computer-Human Interaction}%
  \def\@journalNameShort{ACM Trans. Comput.-Hum. Interact.}%
  \def\@permissionCodeOne{1073-0516}%
  \def\@permissionCodeTwo{1557-7325}%
\or % TOCL
  \def\@journalName{ACM Transactions on Computational Logic}%
  \def\@journalNameShort{ACM Trans. Comput. Logic}%
  \def\@permissionCodeOne{1529-3785}%
  \def\@permissionCodeTwo{1557-945X}%
\or % TOCS
  \def\@journalName{ACM Transactions on Computer Systems}%
  \def\@journalNameShort{ACM Trans. Comput. Syst.}%
  \def\@permissionCodeOne{0734-2071}%
  \def\@permissionCodeTwo{1557-7333}%
\or % TOCT
  \def\@journalName{ACM Transactions on Computation Theory}%
  \def\@journalNameShort{ACM Trans. Comput. Theory}%
  \def\@permissionCodeOne{1942-3454}%
  \def\@permissionCodeTwo{1942-3462}%
\or % TODAES
  \def\@journalName{ACM Transactions on Design Automation of Electronic Systems}%
  \def\@journalNameShort{ACM Trans. Des. Autom. Electron. Syst.}%
  \def\@permissionCodeOne{1084-4309}%
  \def\@permissionCodeTwo{1557-7309}%
\or % TODS
  \def\@journalName{ACM Transactions on Database Systems}%
  \def\@journalNameShort{ACM Trans. Datab. Syst.}%
  \def\@permissionCodeOne{0362-5915}%
  \def\@permissionCodeTwo{1557-4644}%
\or % TOG
  \def\@journalName{ACM Transactions on Graphics}%
  \def\@journalNameShort{ACM Trans. Graph.}%
  \def\@permissionCodeOne{0730-0301}%
  \def\@permissionCodeTwo{1557-7368}%
\or % TOIS
  \def\@journalName{ACM Transactions on Information Systems}%
  \def\@journalNameShort{ACM Trans. Inf. Syst.}%
  \def\@permissionCodeOne{1046-8188}%
  \def\@permissionCodeTwo{1558-2868}%
\or % TOIT
  \def\@journalName{ACM Transactions on Internet Technology}%
  \def\@journalNameShort{ACM Trans. Internet Technol.}%
  \def\@permissionCodeOne{1533-5399}%
  \def\@permissionCodeTwo{1557-6051}%
\or % TOMACS
  \def\@journalName{ACM Transactions on Modeling and Computer Simulation}%
  \def\@journalNameShort{ACM Trans. Model. Comput. Simul.}%
  \def\@permissionCodeOne{1049-3301}%
  \def\@permissionCodeTwo{1558-1195}%
\or % TOMM
  \def\@journalName{ACM Transactions on Multimedia Computing, Communications and Applications}%
  \def\@journalNameShort{ACM Trans. Multimedia Comput. Commun. Appl.}%
  \def\@permissionCodeOne{1551-6857}%
  \def\@permissionCodeTwo{1551-6865}%
\or % TOMPECS
  \def\@journalName{ACM Transactions on Modeling and Performance Evaluation of Computing Systems}%
  \def\@journalNameShort{ACM Trans. Model. Perform. Eval. Comput. Syst.}%
  \def\@permissionCodeOne{2376-3639}%
  \def\@permissionCodeTwo{2376-3647}%
\or % TOMS
  \def\@journalName{ACM Transactions on Mathematical Software}%
  \def\@journalNameShort{ACM Trans. Math. Softw.}%
  \def\@permissionCodeOne{0098-3500}%
  \def\@permissionCodeTwo{1557-7295}%
\or % TOPC
  \def\@journalName{ACM Transactions on Parallel Computing}%
  \def\@journalNameShort{ACM Trans. Parallel Comput.}%
  \def\@permissionCodeOne{2329-4949}%
  \def\@permissionCodeTwo{2329-4957}%
\or % TOPLAS
  \def\@journalName{ACM Transactions on Programming Languages and Systems}%
  \def\@journalNameShort{ACM Trans. Program. Lang. Syst.}%
  \def\@permissionCodeOne{0164-0925}%
  \def\@permissionCodeTwo{1558-4593}%
\or % TOPML
  \def\@journalName{ACM Transactions on Probabilistic Machine Learning}%
  \def\@journalNameShort{ACM Trans. Probab. Mach. Learn.}%
  \def\@permissionCodeOne{2836-8924}%
  \def\@permissionCodeTwo{2836-8924}%
\or % TOPS
  \def\@journalName{ACM Transactions on Privacy and Security}%
  \def\@journalNameShort{ACM Trans. Priv. Sec.}%
  \def\@permissionCodeOne{2471-2566}%
  \def\@permissionCodeTwo{2471-2574}%
\or % TORS
  \def\@journalName{ACM Transactions on Recommender Systems}%
  \def\@journalNameShort{ACM Trans. Recomm. Syst.}%
  \def\@permissionCodeOne{2770-6699}%
  \def\@permissionCodeTwo{2770-6699}%
\or % TOS
  \def\@journalName{ACM Transactions on Storage}%
  \def\@journalNameShort{ACM Trans. Storage}%
  \def\@permissionCodeOne{1553-3077}%
  \def\@permissionCodeTwo{1553-3093}%
\or % TOSEM
  \def\@journalName{ACM Transactions on Software Engineering and Methodology}%
  \def\@journalNameShort{ACM Trans. Softw. Eng. Methodol.}%
  \def\@permissionCodeOne{1049-331X}%
  \def\@permissionCodeTwo{1557-7392}%
\or % TOSN
  \def\@journalName{ACM Transactions on Sensor Networks}%
  \def\@journalNameShort{ACM Trans. Sensor Netw.}%
  \def\@permissionCodeOne{1550-4859}%
  \def\@permissionCodeTwo{1550-4867}%
\or % TQC
  \def\@journalName{ACM Transactions on Quantum Computing}%
  \def\@journalNameShort{ACM Trans. Quantum Comput.}%
  \def\@permissionCodeOne{2643-6817}%
  \def\@permissionCodeTwo{2643-6817}%
\or % TRETS
  \def\@journalName{ACM Transactions on Reconfigurable Technology and Systems}%
  \def\@journalNameShort{ACM Trans. Reconfig. Technol. Syst.}%
  \def\@permissionCodeOne{1936-7406}%
  \def\@permissionCodeTwo{1936-7414}%
\or % TSAS
  \def\@journalName{ACM Transactions on Spatial Algorithms and Systems}%
  \def\@journalNameShort{ACM Trans. Spatial Algorithms Syst.}%
  \def\@permissionCodeOne{2374-0353}%
  \def\@permissionCodeTwo{2374-0361}%
\or % TSC
  \def\@journalName{ACM Transactions on Social Computing}%
  \def\@journalNameShort{ACM Trans. Soc. Comput.}%
  \def\@permissionCodeOne{2469-7818}%
  \def\@permissionCodeTwo{2469-7826}%
\or % TSLP
  \def\@journalName{ACM Transactions on Speech and Language Processing}%
  \def\@journalNameShort{ACM Trans. Speech Lang. Process.}%
  \def\@permissionCodeOne{1550-4875}%
  \def\@permissionCodeTwo{2329-9304}%
\or % TWEB
  \def\@journalName{ACM Transactions on the Web}%
  \def\@journalNameShort{ACM Trans. Web}%
  \def\@permissionCodeOne{1559-1131}%
  \def\@permissionCodeTwo{1559-114X}%
\else % FACMP, a dummy journal
  \def\@journalName{ACM Just Accepted}%
  \def\@journalNameShort{ACM Accepted}%
  \def\@permissionCodeOne{XXXX-XXXX}%
\fi
\ClassInfo{\@classname}{Using journal code \@journalCode}%
}{%
  \ClassError{\@classname}{Incorrect journal #1}%
}%
%    \end{macrocode}
% \begin{macro}{\acmJournal}
% \changes{v1.59}{2019/04/20}{Setting \cs{@ACM@journal@bibstrip}}
% And the syntactic sugar around it
%    \begin{macrocode}
\def\acmJournal#1{\setkeys{ACM}{acmJournal=#1}%
  \global\@ACM@journal@bibstriptrue
  \global\@ACM@journal@bibstrip@or@togtrue}
%    \end{macrocode}
%
% \end{macro}
%
% The defaults:
% \changes{v2.04}{2024/03/17}{eISSN is printed, rather than pSSN}
%    \begin{macrocode}
\def\@journalCode@nr{0}
\def\@journalName{}%
\def\@journalNameShort{\@journalName}%
\def\@permissionCodeOne{XXXX-XXXX}%
\def\@permissionCodeTwo{\@permissionCodeOne}%
%    \end{macrocode}
%
%
% \begin{macro}{\acmConference}
% \changes{v1.59}{2019/04/20}{Setting \cs{@ACM@journal@bibstrip}}
% \changes{v2.02}{2024/01/06}{Moved here setting the conference title
% for bibstrip}
% \changes{v2.03}{2024/02/04}{Made setting bibstrip overriding journal}
%   This is the conference command
%    \begin{macrocode}
\newcommand\acmConference[4][]{%
  \gdef\acmConference@shortname{#1}%
  \gdef\acmConference@name{#2}%
  \gdef\acmConference@date{#3}%
  \gdef\acmConference@venue{#4}%
  \ifx\acmConference@shortname\@empty
    \gdef\acmConference@shortname{#2}%
  \fi
  \global\@ACM@journal@bibstripfalse
  \ifx\@acmBooktitle\@empty\relax
    \acmBooktitle{Proceedings of \acmConference@name
      \ifx\acmConference@name\acmConference@shortname\else
      \ (\acmConference@shortname)\fi}%
  \fi
}
\if@ACM@journal\else
\acmConference[Conference'17]{ACM Conference}{July 2017}{Washington,
  DC, USA}%
\fi
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmBooktitle}
% \changes{v1.44}{2017/08/11}{Added macro}
% \changes{v2.01}{2024/01/06}{Moved default conference title to
% conference setting}
% \begin{macro}{\@acmBooktitle}
% \changes{v1.44}{2017/08/11}{Added macro}
%   The book title of the conference:
%    \begin{macrocode}
\def\acmBooktitle#1{\gdef\@acmBooktitle{#1}}
\acmBooktitle{}
%    \end{macrocode}
%
% \end{macro}
% \end{macro}
%
% \begin{macro}{\@editorsAbbrev}
% \changes{v1.44}{2017/08/11}{Added macro}
%   How to abbreviate editors
%    \begin{macrocode}
\def\@editorsAbbrev{(Ed.)}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@acmEditors}
% \changes{v1.44}{2017/08/11}{Added macro}
%   The list of editors
%    \begin{macrocode}
\def\@acmEditors{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\editor}
% \changes{v1.44}{2017/08/11}{Added macro}
%   Add a new editor to the list
%    \begin{macrocode}
\def\editor#1{\ifx\@acmEditors\@empty
    \gdef\@acmEditors{#1}%
  \else
    \gdef\@editorsAbbrev{(Eds.)}%
    \g@addto@macro\@acmEditors{\and#1}%
\fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\subtitle}
%   The subtitle macro
%    \begin{macrocode}
\def\subtitle#1{\def\@subtitle{#1}}
\subtitle{}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\num@authorgroups}
% \changes{v1.15}{2016/06/25}{Renamed}
%   The total number of ``groups''.  Each group is several authors with
%   the same affiliations(s)
%    \begin{macrocode}
\newcount\num@authorgroups
\num@authorgroups=0\relax
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\num@authors}
% \changes{v1.46}{2017/08/27}{Introduced macro}
%   The total number of authors
%    \begin{macrocode}
\newcount\num@authors
\num@authors=0\relax
%    \end{macrocode}
%
% \end{macro}
%
%
%
%
% \begin{macro}{\if@insideauthorgroup}
% \changes{v1.15}{2016/06/25}{Introduced macro}
%  Whether we are continuing an author group
%    \begin{macrocode}
\newif\if@insideauthorgroup
\@insideauthorgroupfalse
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\author}
% \changes{v1.15}{2016/06/25}{Added code for author groups}
% \changes{v1.46}{2017/08/27}{Started counting authors}
% \changes{v1.54}{2018/06/23}{Added submission id in anonymous mode}
% \changes{v1.57}{2018/12/09}{Added warning if \cs{author} has comma}
% \changes{v1.72}{2020/05/04}{Added \cs{detokenize} (Kuldeep S. Meel)}
% \changes{v1.76}{2021/03/15}{Added code for orcid links}
% Adding an author to the list of authors and addresses.
%
% Note that we want to typeset the link to the author's orcid if
% known.  The problem is, we know the orcid only after the author is
% entered.  The trick is based on the idea that |\csname...\endcsname|
% is \cs{relax} unless defined.  Therefore we typeset authors by the
% special macro |\csname typeset@author\the\num@authors\endcsname|,
% which is defined by \cs{orcid} command.
%    \begin{macrocode}
\renewcommand\author[2][]{%
  \IfSubStr{\detokenize{#2}}{,}{\ClassWarning{\@classname}{Do not put several
      authors in the same \string\author\space macro!}}{}%
  \global\advance\num@authors by 1\relax
  \if@insideauthorgroup\else
    \global\advance\num@authorgroups by 1\relax
    \global\@insideauthorgrouptrue
  \fi
  \ifx\addresses\@empty
    \if@ACM@anonymous
      \gdef\addresses{\@author{Anonymous Author(s)%
        \ifx\@acmSubmissionID\@empty\else\\Submission Id:
          \@acmSubmissionID\fi}}%
      \gdef\authors{Anonymous Author(s)}%
    \else
      \expandafter\gdef\expandafter\addresses\expandafter{%
        \expandafter\@author\expandafter{%
          \csname typeset@author\the\num@authors\endcsname{#2}}}%
      \gdef\authors{#2}%
    \fi
  \else
    \if@ACM@anonymous\else
    \expandafter\g@addto@macro\expandafter\addresses\expandafter{%
      \expandafter\and\expandafter\@author\expandafter{%
        \csname typeset@author\the\num@authors\endcsname{#2}}}%
      \g@addto@macro\authors{\and#2}%
    \fi
  \fi
  \if@ACM@anonymous
    \ifx\shortauthors\@empty
      \gdef\shortauthors{Anon.
        \ifx\@acmSubmissionID\@empty\else Submission Id:
        \@acmSubmissionID\fi}%
    \fi
  \else
    \def\@tempa{#1}%
    \ifx\@tempa\@empty
      \ifx\shortauthors\@empty
        \gdef\shortauthors{#2}%
      \else
        \g@addto@macro\shortauthors{\and#2}%
      \fi
    \else
      \ifx\shortauthors\@empty
        \gdef\shortauthors{#1}%
      \else
        \g@addto@macro\shortauthors{\and#1}%
      \fi
    \fi
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\affiliation}
% \changes{v1.15}{2016/06/25}{Added code for author groups}
%   The macro \cs{affiliation} mimics \cs{address} from |amsart|.
%   Note that it has an optional argument, which we use differently
%   from |amsart|.
%    \begin{macrocode}
\newcommand{\affiliation}[2][]{%
  \global\@insideauthorgroupfalse
  \if@ACM@anonymous\else
    \g@addto@macro\addresses{\affiliation{#1}{#2}}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@affiliation@obeypunctuation}
% \changes{v1.33}{2017/03/28}{Added macro}
%   Whether to use the author's punctuation (false by default, which adds
%   American-style address punctuation)
%    \begin{macrocode}
\define@boolkey+{@ACM@affiliation@}[@ACM@affiliation@]{obeypunctuation}%
[true]{}{\ClassError{\@classname}{The option obeypunctuation can be either true or false}}
%    \end{macrocode}
%
% \end{macro}
%
%
%
% \begin{macro}{\additionalaffiliation}
% \changes{v1.31}{2017/03/04}{Added macro}
%   Additional affiliations go to footnotes
%    \begin{macrocode}
\def\additionalaffiliation#1{\authornote{\@additionalaffiliation{#1}}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@additionalaffiliation}
% \changes{v1.31}{2017/03/04}{Added macro}
%   Process \cs{additionalaffiliation} inside \cs{authornote}
%    \begin{macrocode}
\def\@additionalaffiliation#1{\bgroup
  \def\position##1{\ignorespaces}%
  \def\institution##1{##1\ignorespaces}%
  \def\department{\@ifnextchar[{\@department}{\@department[]}}%
  \def\@department[##1]##2{##2, \ignorespaces}%
  \let\city\position
  \let\state\position
  \let\country\position
  Also with #1\unskip.\egroup}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\email}
% \changes{v1.57}{2018/12/09}{Added warning if \cs{author} has comma}
%   The macro \cs{email} mimics \cs{email} from |amsart|.
%   Again, it has an optional argument that we do not currently need
%   but keep for possible future use.
%    \begin{macrocode}
\renewcommand{\email}[2][]{%
  \IfSubStr{#2}{,}{\ClassWarning{\@classname}{Do not put several
      addresses in the same \string\email\space macro!}}{}%
  \if@ACM@anonymous\else
    \g@addto@macro\addresses{\email{#1}{#2}}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\orcid}
% \changes{v1.15}{2016/06/25}{Introduced macro}
% \changes{v1.76}{2021/03/15}{Added code for orcid links}
% \changes{2.13}{2025/01/15}{Protected orchidcite against uppercasing} 
%   We redefine the command to typeset the current author
%    \begin{macrocode}
\def\orcid#1{\unskip\ignorespaces%
  \protected\def\orcidsite{https://orcid.org/}%
  \IfBeginWith{#1}{http}{%
    \expandafter\gdef\csname
        typeset@author\the\num@authors\endcsname##1{%
          \href{#1}{##1}}}{%
    \expandafter\gdef\csname
        typeset@author\the\num@authors\endcsname##1{%
          \href{\orcidsite#1}{##1}}}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\authorsaddresses}
% \changes{v1.46}{2017/08/27}{Introduced macro}
% Setting up authors' addresses
%    \begin{macrocode}
\def\authorsaddresses#1{\def\@authorsaddresses{#1}}
\authorsaddresses{\@mkauthorsaddresses}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mktranslatedkeywords}
% \changes{v1.83}{2022/02/20}{Added macro}
% We have two argument: the language to typeset keywords and the
% keywords.
%    \begin{macrocode}
\newcommand\@mktranslatedkeywords[2]{\bgroup
  \selectlanguage{#1}%
  {\@specialsection{\keywordsname}%
    \noindent#2\par}\egroup}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\@titlenotes}
%   The title notes
%    \begin{macrocode}
\def\@titlenotes{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\titlenote}
%   Adding a note to the title
%    \begin{macrocode}
\def\titlenote#1{%
  \g@addto@macro\@title{\footnotemark}%
  \if@ACM@anonymous
    \g@addto@macro\@titlenotes{%
      \stepcounter{footnote}\footnotetext{Title note}}%
  \else
    \g@addto@macro\@titlenotes{\stepcounter{footnote}\footnotetext{#1}}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@subtitlenotes}
%   The subtitle notes
%    \begin{macrocode}
\def\@subtitlenotes{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\subtitlenote}
%   Adding a note to the subtitle
%    \begin{macrocode}
\def\subtitlenote#1{%
  \g@addto@macro\@subtitle{\footnotemark}%
  \if@ACM@anonymous
    \g@addto@macro\@subtitlenotes{%
      \stepcounter{footnote}\footnotetext{Subtitle note}}%
  \else
    \g@addto@macro\@subtitlenotes{%
      \stepcounter{footnote}\footnotetext{#1}}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@authornotes}
%   The author notes
%    \begin{macrocode}
\def\@authornotes{}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\authornote}
%   Adding a note to the author
%    \begin{macrocode}
\def\authornote#1{%
  \if@ACM@anonymous\else
    \g@addto@macro\addresses{\@authornotemark}%
    \g@addto@macro\@authornotes{%
      \stepcounter{footnote}\footnotetext{#1}}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\authornotemark}
% \changes{v1.39}{2017/05/14}{Added macro}
% Adding a footnote mark to the authors
%    \begin{macrocode}
\newcommand\authornotemark[1][\relax]{%
  \if@ACM@anonymous\else
    \ifx#1\relax\relax\relax
      \g@addto@macro\addresses{\@authornotemark}%
    \else
      \g@addto@macro\addresses{\@@authornotemark{#1}}%
    \fi
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmVolume}
% \changes{v1.17}{2016/07/10}{The default is now numerical}
%   The current volume
%    \begin{macrocode}
\def\acmVolume#1{\def\@acmVolume{#1}}
\acmVolume{1}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmNumber}
% \changes{v1.17}{2016/07/10}{The default is now numerical}
%   The current number
%    \begin{macrocode}
\def\acmNumber#1{\def\@acmNumber{#1}}
\acmNumber{1}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmArticle}
% \changes{v1.17}{2016/07/10}{The default is now numerical}
% \changes{v1.44}{2017/08/111}{The default is now empty}
%   The current article
%    \begin{macrocode}
\def\acmArticle#1{\def\@acmArticle{#1}}
\acmArticle{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmArticleSeq}
% \changes{v1.04}{2016/04/26}{Name change by Matthew Fluet}
% \changes{v1.44}{2017/08/11}{Now acmArticle might be empty}
%   The sequence number
%    \begin{macrocode}
\def\acmArticleSeq#1{\def\@acmArticleSeq{#1}}
\acmArticleSeq{\@acmArticle}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmYear}
% \changes{v1.17}{2016/07/10}{The default is now numerical}
% \changes{v1.31}{2017/03/04}{The default now is the current year
% (Matteo Riondato)}
%   The current year
%    \begin{macrocode}
\def\acmYear#1{\def\@acmYear{#1}}
\acmYear{\the\year}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmMonth}
% \changes{v1.17}{2016/07/09}{The default must be numerical.   Closes \#50.}
% \changes{v1.31}{2017/03/04}{The default now is the current month
% (Matteo Riondato)}
%   The current month
%    \begin{macrocode}
\def\acmMonth#1{\def\@acmMonth{#1}}
\acmMonth{\the\month}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@acmPubDate}
%   The publication date
%    \begin{macrocode}
\def\@acmPubDate{\ifcase\@acmMonth\or
  January\or February\or March\or April\or May\or June\or
  July\or August\or September\or October\or November\or
  December\fi~\@acmYear}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmPrice}
% \changes{v2.00}{2023/10/22}{Deleted macro}
% \changes{v2.01}{2023/12/29}{Now the macro produces a warning}
% The price---obsolete
%    \begin{macrocode}
\def\acmPrice#1{\ClassWarning{\@classname}{The macro \string\acmPrice
    is obsolete.  ACM no longer prints the price in bibstrip.}}
%    \end{macrocode}
% \end{macro}
%
%
% \begin{macro}{\acmSubmissionID}
% \changes{v1.33}{2017/03/29}{Added macro}
%   The submission ID
%    \begin{macrocode}
\def\acmSubmissionID#1{\def\@acmSubmissionID{#1}}
\acmSubmissionID{}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\acmISBN}
% \changes{v1.04}{2016/04/26}{Name change by Matthew Fluet}
%   The book ISBN
%    \begin{macrocode}
\def\acmISBN#1{\def\@acmISBN{#1}}
\acmISBN{978-x-xxxx-xxxx-x/YYYY/MM}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmDOI}
% \changes{v1.04}{2016/04/26}{Name change by Matthew Fluet}
%   The paper DOI
%    \begin{macrocode}
\def\acmDOI#1{\def\@acmDOI{#1}}
\acmDOI{10.1145/nnnnnnn.nnnnnnn}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@badge}
% \changes{v1.06}{2016/05/01}{Added macro}
% \changes{v1.89}{2022/12/27}{Deleted macro}
% \end{macro}
%
% \begin{macro}{\@ACM@badge@width}
% \changes{v1.06}{2016/05/01}{Added macro}
%   The width of the badge
%    \begin{macrocode}
\newlength\@ACM@badge@width
\setlength\@ACM@badge@width{3pc}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@ACM@title@width}
% \changes{v1.06}{2016/05/01}{Added macro}
%   The width of the title
%    \begin{macrocode}
\newlength\@ACM@title@width
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@ACM@badge@skip}
% \changes{v1.06}{2016/05/01}{Added macro}
% \changes{v1.89}{2022/12/27}{Redefined macro}
%   The spacing between badges
%    \begin{macrocode}
\newlength\@ACM@badge@skip
\setlength\@ACM@badge@skip{1pt}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@acmBadgeR}
% \changes{v1.89}{2022/12/27}{Introduced macro}
%    \begin{macrocode}
\def\@acmBadgeR{}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\@acmBadgeL}
% \changes{v1.89}{2022/12/27}{Introduced macro}
%    \begin{macrocode}
\def\@acmBadgeL{}
%    \end{macrocode}
% \end{macro}
% 
% \begin{macro}{\acmBadgeR}
% \changes{v1.06}{2016/05/01}{Added macro}
% \changes{v1.89}{2022/12/27}{Redefined macro}
%   Setting the right badge
%    \begin{macrocode}
\newcommand\acmBadgeR[2][]{%
  \ifx\@acmBadgeR\@empty
    \gdef\@acmBadgeR{%
      \smash{%
        \raisebox{0.5\height}{%
          \href{#1}{\includegraphics[width=\@ACM@badge@width]{#2}}}}}%
  \else
    \g@addto@macro{\@acmBadgeR}{%
      \hspace{\@ACM@badge@skip}%
      \smash{%
        \raisebox{0.5\height}{%
          \href{#1}{\includegraphics[width=\@ACM@badge@width]{#2}}}}}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmBadgeL}
% \changes{v1.06}{2016/05/01}{Added macro}
% \changes{v1.89}{2022/12/27}{Redefined macro}
%   Setting the left badge
%    \begin{macrocode}
\newcommand\acmBadgeL[2][]{%
  \ifx\@acmBadgeL\@empty
    \gdef\@acmBadgeL{%
      \smash{%
        \raisebox{0.5\height}{%
          \href{#1}{\includegraphics[width=\@ACM@badge@width]{#2}}}}}%
  \else
    \g@addto@macro{\@acmBadgeL}{%
      \hspace{\@ACM@badge@skip}%
      \smash{%
        \raisebox{0.5\height}{%
          \href{#1}{\includegraphics[width=\@ACM@badge@width]{#2}}}}}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmBadge}
% \changes{v1.89}{2023/01/07}{Added macro}
% Just a syntax sugar for \cs{acmBadgeR}
%    \begin{macrocode}
\let\acmBadge=\acmBadgeR
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\startPage}
%   The start page of the paper
%    \begin{macrocode}
\def\startPage#1{\def\@startPage{#1}}
\startPage{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\terms}
% \changes{v1.34}{2017/04/09}{The \cs{terms} command now just produces
% a warning}
%   Terms are obsolete.  We use CCS now.
%    \begin{macrocode}
\def\terms#1{\ClassWarning{\@classname}{The command \string\terms{} is
    obsolete.  I am going to ignore it}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\keywords}
%   Keywords are mostly obsolete.  We use CCS now.  Still it makes
%   sense to keep them for compatibility.
%    \begin{macrocode}
\def\keywords#1{\def\@keywords{#1}}
\let\@keywords\@empty
%    \end{macrocode}
%
% \changes{1.66}{2019/12/18}{Added warning for missing keywords}
%    \begin{macrocode}
\AtEndDocument{\if@ACM@nonacm\else\ifx\@keywords\@empty
  \ifnum\getrefnumber{TotPages}>2\relax
  \ClassWarningNoLine{\@classname}{ACM keywords are mandatory
    for papers over two pages}%
  \fi\fi\fi}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{abstract}
%   The |amsart| package puts |abstract| in a box.  Since we do not
%   know whether we will use two-column mode, we prefer to save the text
%    \begin{macrocode}
\renewenvironment{abstract}{\Collect@Body\@saveabstract}{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@saveabstract}
%   And saving the abstract
%    \begin{macrocode}
\long\def\@saveabstract#1{\if@ACM@maketitle@typeset
  \ClassError{\@classname}{Abstract must be defined before maketitle
    command. Please move it!}\fi
  \long\gdef\@abstract{#1}}
\@saveabstract{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@lempty}
%   The long version of \cs{@empty} (to compare with \cs{@abstract})
%    \begin{macrocode}
\long\def\@lempty{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@printccs}
% \changes{v1.27}{2016/12/29}{Typo corrected}
%   Whether to print CCS
%    \begin{macrocode}
\define@boolkey+{@ACM@topmatter@}[@ACM@]{printccs}[true]{%
  \if@ACM@printccs
    \ClassInfo{\@classname}{Printing CCS}%
  \else
    \ClassInfo{\@classname}{Suppressing CCS}%
  \fi}{\ClassError{\@classname}{The option printccs can be either true or false}}
%    \end{macrocode}
%
% \end{macro}
% \begin{macro}{\if@ACM@printacmref}
% \changes{v1.17}{2016/07/10}{Renamed from \cs{if@ACM@printbib}}
%   Whether to print the ACM bibstrip
%    \begin{macrocode}
\define@boolkey+{@ACM@topmatter@}[@ACM@]{printacmref}[true]{%
  \if@ACM@printacmref
    \ClassInfo{\@classname}{Printing bibformat}%
  \else
    \ClassInfo{\@classname}{Suppressing bibformat}%
  \fi}{\ClassError{\@classname}{The option printacmref can be either true or false}}
%    \end{macrocode}
%
% \changes{1.66}{2019/12/18}{Added warning for longer papers}
%    \begin{macrocode}
\AtEndDocument{\if@ACM@nonacm\else\if@ACM@printacmref\else
  \ifnum\getrefnumber{TotPages}>1\relax
  \ClassWarningNoLine{\@classname}{%
    ACM reference format is mandatory \MessageBreak
    for papers over one page. \MessageBreak
    Please add printacmref=true to the \MessageBreak
    \string\settopmatter\space command.}%
  \fi\fi\fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@printfolios}
% \changes{v1.13}{2016/06/06}{Added macro}
%   Whether to print folios
%    \begin{macrocode}
\define@boolkey+{@ACM@topmatter@}[@ACM@]{printfolios}[true]{%
  \if@ACM@printfolios
    \ClassInfo{\@classname}{Printing folios}%
  \else
    \ClassInfo{\@classname}{Suppressing folios}%
  \fi}{\ClassError{\@classname}{The option printfolios can be either true or false}}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\@ACM@authorsperrow}
% \changes{v1.36}{2017/05/13}{Added macro}
%   The number of authors per row.  0 means use the default algorithm.
%    \begin{macrocode}
\define@cmdkey{@ACM@topmatter@}[@ACM@]{authorsperrow}[0]{%
  \IfInteger{#1}{\ClassInfo{\@classname}{Setting authorsperrow to
      #1}}{\ClassWarning{\@classname}{The parameter authorsperrow must be
      numerical. Ignoring the input #1}\gdef\@ACM@authorsperrow{0}}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\settopmatter}
%   The usual syntactic sugar
%    \begin{macrocode}
\def\settopmatter#1{\setkeys{@ACM@topmatter@}{#1}}
%    \end{macrocode}
%
% \end{macro}
%
%
% \changes{v1.13}{2016/06/06}{Print bibliographic information by
% default for the proceedings}
% Now the settings
%    \begin{macrocode}
\settopmatter{printccs=true, printacmref=true}
\if@ACM@manuscript
  \settopmatter{printfolios=true}
\else
  \if@ACM@journal
    \settopmatter{printfolios=true}
  \else
    \settopmatter{printfolios=false}
  \fi
\fi
\settopmatter{authorsperrow=0}
%    \end{macrocode}
%
% \begin{macro}{\@received}
%   The container for the paper history
%    \begin{macrocode}
\def\@received{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\received}
%   The \cs{received} command
%    \begin{macrocode}
\newcommand\received[2][]{\def\@tempa{#1}%
  \ifx\@tempa\@empty
    \ifx\@received\@empty
      \gdef\@received{Received #2}%
    \else
      \g@addto@macro{\@received}{; revised #2}%
    \fi
  \else
    \ifx\@received\@empty
      \gdef\@received{#1 #2}%
    \else
      \g@addto@macro{\@received}{; #1 #2}%
    \fi
  \fi}
\AtEndDocument{%
  \ifx\@received\@empty\else
    \par\bigskip\noindent{\small\normalfont\@received\par}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acmArticleType}
% \changes{v1.87}{2022/08/13}{Introduced macro}
% Article type.  We set up the color.
%    \begin{macrocode}
\define@choicekey+{ACM}{articletype}[\ACM@ArticleType\ACM@ArticleType@nr]{%
  Research,Review,Discussion,Invited,Position}[Review]{%
  \ifcase\ACM@ArticleType@nr
  \relax % Research
     \colorlet{@ACM@Article@color}{ACMBlue}%
  \or % Review
     \colorlet{@ACM@Article@color}{ACMOrange}%     
  \or % Discussion
     \colorlet{@ACM@Article@color}{ACMGreen}%     
  \or % Invited
     \colorlet{@ACM@Article@color}{ACMPurple}%     
  \or % Position
     \colorlet{@ACM@Article@color}{ACMRed}%     
  \fi
}{%
 \ClassError{\@classname}{Article Type must be Research, Review,\MessageBreak
   Discussion, Invited, or Position}}
\def\acmArticleType#1{\setkeys{ACM}{articletype=#1}}
\if@ACM@acmcp
\acmArticleType{Research}%
\fi
 %    \end{macrocode}
% 
% \end{macro}
% \begin{macro}{\@ACM@color@frame}
% \changes{v1.87}{2022/08/13}{Introduced macro}
% \changes{v1.89}{2022/12/25}{Added saving the position of the bottom}
% The colored frame for the acmcp
%    \begin{macrocode}
\newenvironment{@ACM@color@frame}{%
  \def\FrameCommand{\hspace*{-6.5pc}%
    \colorbox{@ACM@Article@color!10!white}}%
  \MakeFramed {\advance\hsize
    -6.5pc\relax\FrameRestore}}{\zsaveposy{@ACM@acmcpframe@y}%
  \endMakeFramed}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\acmCodeDataLink}
% \changes{v1.87}{2022/08/13}{Introduced macro}
% \changes{v1.87}{2022/08/28}{Added possibility to have several links}
% The code link
%    \begin{macrocode}
\def\acmCodeLink#1{%
  \ifx\@acmCodeDataLink\@empty
     \gdef\@acmCodeDataLink{\url{#1}}%
  \else
     \g@addto@macro{\@acmCodeDataLink}{\\ \url{#1}}%
  \fi}
\def\@acmCodeDataLink{}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\acmContributions}
% \changes{v1.91}{2023/06/22}{Restored the macro}
% The authors' contrubution statement
%    \begin{macrocode}
\def\acmContributions#1{\gdef\@acmContributions{#1}}
\acmContributions{}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\acmDataLink}
% \changes{v1.87}{2022/08/13}{Introduced macro}
% \changes{v1.87}{2022/08/28}{Now this is the same as the code link}
% The code link
%    \begin{macrocode}
\let\acmDataLink\acmCodeLink
%    \end{macrocode}
% 
% \end{macro}
%
%
% 
%\subsection{Concepts system}
%\label{sec:concepts}
%
% We exclude |CCSXML| stuff generated by the ACM system:
%    \begin{macrocode}
\RequirePackage{comment}
\excludecomment{CCSXML}
%    \end{macrocode}
%
% \begin{macro}{\@concepts}
%   This is the storage macro and counter for concepts
%    \begin{macrocode}
\let\@concepts\@empty
\newcounter{@concepts}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\ccsdesc}
% \changes{v1.40}{2017/05/27}{Now we can parse |Significance~General| nodes}
%   The first argument is the significance, the second is the
%   concept(s)
%    \begin{macrocode}
\newcommand\ccsdesc[2][100]{%
  \ccsdesc@parse#1~#2~~\ccsdesc@parse@end}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\ccsdesc@parse}
% \changes{v1.28}{2017/01/04}{Change from \cs{to} to
% \cs{textrightarrow} (Matteo Riondato)}
% \changes{v1.29}{2017/01/22}{Add spacing after bullet and around
% rightarrow; semicolon separators no longer in bold/italic (John Wickerson)}
% \changes{v1.40}{2017/05/27}{Now we can parse |Significance~General| nodes}
% \changes{v1.56}{2018/10/23}{End the concepts list with a period
% (Philip Quinn)}
% \changes{v1.69}{2019/02/06}{Use math right arrow in text}
%   The parser of the expression |Significance~General~Specific| (we need
%   |textcomp| for |\textrightarrow|).  Note that |Specific| can be empty!
%    \begin{macrocode}
\def\textrightarrow{$\rightarrow$}
\def\ccsdesc@parse#1~#2~#3~{%
  \stepcounter{@concepts}%
  \expandafter\ifx\csname CCS@General@#2\endcsname\relax
    \expandafter\gdef\csname CCS@General@#2\endcsname{\textbullet\
      \textbf{#2}}%
    \expandafter\gdef\csname CCS@Punctuation@#2\endcsname{; }%
    \expandafter\gdef\csname CCS@Specific@#2\endcsname{}%
  \g@addto@macro{\@concepts}{\csname CCS@General@#2\endcsname
    \csname CCS@Punctuation@#2\endcsname
    \csname CCS@Specific@#2\endcsname}%
  \fi
  \ifx#3\relax\relax\else
    \expandafter\gdef\csname CCS@Punctuation@#2\endcsname{
      \textrightarrow\ }%
    \expandafter\g@addto@macro\expandafter{\csname CCS@Specific@#2\endcsname}{%
     \addtocounter{@concepts}{-1}%
     \ifnum#1>499\textbf{#3}\else
     \ifnum#1>299\textit{#3}\else
     #3\fi\fi\ifnum\value{@concepts}=0.\else; \fi}%
  \fi
\ccsdesc@parse@finish}
%    \end{macrocode}
%
% \changes{1.66}{2019/12/18}{Added warning for missing concepts}
%    \begin{macrocode}
\AtEndDocument{\if@ACM@nonacm\else\ifx\@concepts\@empty\relax
  \ifnum\getrefnumber{TotPages}>2\relax
  \ClassWarningNoLine{\@classname}{CCS concepts are mandatory
    for papers over two pages}%
  \fi\fi\fi}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\ccdesc@parse@finish}
% \changes{v1.40}{2017/05/27}{Added macro}
%   Gobble everything to |\ccsdesc@parse@end|
%    \begin{macrocode}
\def\ccsdesc@parse@finish#1\ccsdesc@parse@end{}
%    \end{macrocode}
%
% \end{macro}
%
%\subsection{Copyright system}
%\label{sec:copyright}
%
% This is from |acmcopyright.sty|
%
% \begin{macro}{\if@printcopyright}
%   Whether to print a copyright symbol
%    \begin{macrocode}
\newif\if@printcopyright
\@printcopyrighttrue
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@printpermission}
%   Whether to print the permission block
%    \begin{macrocode}
\newif\if@printpermission
\@printpermissiontrue
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@acmowned}
%   Whether the ACM owns the rights to the paper
%    \begin{macrocode}
\newif\if@acmowned
\@acmownedtrue
%    \end{macrocode}
%
% \end{macro}
%
% \changes{v1.10}{2016/05/22}{Changes of `licensedothergov' wording}
% \changes{v1.35}{2017/04/22}{If the copyright is set to usgov or
% rigtsretained, the price is suppressed}
% \changes{v1.51}{2018/04/05}{Suppress price if the copyright is set
% to iw3c2w3 or iw3c2w3g}
% \changes{v1.51}{2018/04/05}{Corrected the bug with price suppression}
% \changes{v1.85}{2022/05/08}{Added CC licenses}
% \changes{v2.00}{2023/10/22}{Deleted acmPrice}
% \changes{v2.00}{2023/10/22}{Deleted acmPrice}
% Keys:
%    \begin{macrocode}
\define@choicekey*{ACM@}{acmcopyrightmode}[%
  \acm@copyrightinput\acm@copyrightmode]{none,%
    acmcopyright,acmlicensed,rightsretained,%
    usgov,usgovmixed,cagov,cagovmixed,licensedusgovmixed,%
    licensedcagov,licensedcagovmixed,othergov,licensedothergov,%
    iw3c2w3,iw3c2w3g,cc}{%
  \@printpermissiontrue
  \@printcopyrighttrue
  \@acmownedtrue
  \ifnum\acm@copyrightmode=0\relax % none
   \@printpermissionfalse
   \@printcopyrightfalse
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=2\relax % acmlicensed
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=3\relax % rightsretained
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=4\relax % usgov
   \@printpermissiontrue
   \@printcopyrightfalse
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=6\relax % cagov
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=8\relax % licensedusgovmixed
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=9\relax % licensedcagov
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=10\relax % licensedcagovmixed
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=11\relax % othergov
   \@acmownedtrue
  \fi
  \ifnum\acm@copyrightmode=12\relax % licensedothergov
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=13\relax % iw3c2w3
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=14\relax % iw3c2w3g
   \@acmownedfalse
  \fi
  \ifnum\acm@copyrightmode=15\relax % cc
   \@acmownedfalse
  \fi}
%    \end{macrocode}
%
% \begin{macro}{\setcopyright}
%   This is the syntactic sugar around setting keys.
%    \begin{macrocode}
\def\setcopyright#1{\setkeys{ACM@}{acmcopyrightmode=#1}}
\setcopyright{acmlicensed}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\setcctype}
% \changes{v1.85}{2022/05/08}{Added macro}
% The type of Creative Commons license used
%    \begin{macrocode}
\newcommand\setcctype[2][4.0]{%
  \def\ACM@cc@version{#1}%
  \def\ACM@cc@type{#2}}
\setcctype{by}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\@copyrightowner}
% \changes{v1.40}{2017/06/16}{Added new copyright version:
% licensedcagov}
% \changes{v1.52}{2018/04/09}{Rewording of licenses}
% \changes{v1.85}{2022/05/08}{Added CC licenses}
% \changes{v2.00}{2023/10/22}{Changed wording}
% \changes{v2.04}{2024/03/28}{Changed wording}
%   Here is the owner of the copyright
%    \begin{macrocode}
\def\@copyrightowner{%
  \ifcase\acm@copyrightmode\relax % none
  \or % acmcopyright
  ACM\@.
  \or % acmlicensed
  Copyright held by the owner/author(s). Publication rights licensed to
  ACM\@.
  \or % rightsretained
  Copyright held by the owner/author(s).
  \or % usgov
  \or % usgovmixed
  Copyright held by the owner/author(s).
  \or % cagov
  Copyright Crown in Right of Canada.
  \or %cagovmixed
  Copyright held by the owner/author(s).
  \or %licensedusgovmixed
  Copyright held by the owner/author(s). Publication rights licensed to
  ACM\@.
  \or % licensedcagov
  Copyright held by the owner/author(s). 
  \or %licensedcagovmixed
  Copyright held by the owner/author(s). Publication rights licensed to
  ACM\@.
  \or % othergov
  Copyright held by the owner/author(s). 
  \or % licensedothergov
  Copyright held by the owner/author(s). Publication rights licensed to
  ACM\@.
  \or % ic2w3www
  IW3C2 (International World Wide Web Conference Committee), published
  under Creative Commons CC-BY~4.0 License.
  \or % ic2w3wwwgoogle
  IW3C2 (International World Wide Web Conference Committee), published
  under Creative Commons CC-BY-NC-ND~4.0 License.
  \or % cc
  Copyright held by the owner/author(s).
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@formatdoi}
% \changes{v1.03}{2016/04/22}{Added macro}
% \changes{v1.32}{2017/04/07}{New doi format}
%   Print a clickable DOI
%    \begin{macrocode}
\def\@formatdoi#1{\url{https://doi.org/#1}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@ACM@copyright@check@cc}
% \changes{v1.87}{2022/07/30}{Added macro}
% \changes{v2.00}{2023/10/22}{Moved to warning}
% \changes{v2.09}{2024/08/25}{Deleted macro}
% This macro is no longer needed due to the change in ACM policy.  
% \end{macro}
%
% \begin{macro}{\@copyrightpermission}
% \changes{v1.40}{2017/06/16}{Added new copyright version: licensedcagov}
% \changes{v1.50}{2018/02/07}{New copyright statements for iw3c2w3[g]}
% \changes{v1.52}{2018/04/09}{Another rewording of copyright
% statements for iw3c2w3[g]}
% \changes{v1.85}{2022/05/08}{Added CC licenses}
% \changes{v1.87}{2022/07/30}{CC licenses now are allowed only for
% non-acm or ACM engage publications}
% \changes{v2.09}{2024/08/25}{Deleted checking whether CC licenses are
% allowed}
% \changes{v2.11}{2024/12/21}{Changed URL for CC licenses}
%   The canned permission block.
%    \begin{macrocode}
\def\@copyrightpermission{%
  \ifcase\acm@copyrightmode\relax % none
  \or % acmcopyright
   Permission to make digital or hard copies of all or part of this
   work for personal or classroom use is granted without fee provided
   that copies are not made or distributed for profit or commercial
   advantage and that copies bear this notice and the full citation on
   the first page. Copyrights for components of this work owned by
   others than ACM must be honored. Abstracting with credit is
   permitted. To copy otherwise, or republish, to post on servers or
   to redistribute to lists, requires prior specific permission
   and\hspace*{.5pt}/or 
   a fee. Request <NAME_EMAIL>.
  \or % acmlicensed
   Permission to make digital or hard copies of all or part of this
   work for personal or classroom use is granted without fee provided
   that copies are not made or distributed for profit or commercial
   advantage and that copies bear this notice and the full citation on
   the first page. Copyrights for components of this work owned by
   others than the author(s) must be honored. Abstracting with credit
   is permitted. To copy otherwise, or republish, to post on servers
   or to redistribute to lists, requires prior specific permission
   and\hspace*{.5pt}/or a fee. Request permissions from
   <EMAIL>. 
  \or % rightsretained
   Permission to make digital or hard copies of all or part of this
   work for personal or classroom use is granted without fee provided
   that copies are not made or distributed for profit or commercial
   advantage and that copies bear this notice and the full citation on
   the first page. Copyrights for third-party components of this work
   must be honored. For all other uses, contact the
   owner\hspace*{.5pt}/author(s). 
  \or % usgov
   This paper is authored by an employee(s) of the United States
   Government and is in the public domain. Non-exclusive copying or
   redistribution is allowed, provided that the article citation is
   given and the authors and agency are clearly identified as its
   source. Request permissions from
   owner\hspace*{.5pt}/author(s). 
  \or % usgovmixed
   ACM acknowledges that this contribution was authored or co-authored
   by an employee, contractor, or affiliate of the United States
   government. As such, the United States government retains a
   nonexclusive, royalty-free right to publish or reproduce this
   article, or to allow others to do so, for government purposes
   only. Request permissions from owner\hspace*{.5pt}/author(s). 
  \or % cagov
   This article was authored by employees of the Government of
   Canada. As such, the Canadian government retains all interest in
   the copyright to this work and grants to ACM a nonexclusive,
   royalty-free right to publish or reproduce this article, or to
   allow others to do so, provided that clear attribution is given
   both to the authors and the Canadian government agency employing
   them. Permission to make digital or hard copies for personal or
   classroom use is granted. Copies must bear this notice and the full
   citation on the first page. Copyrights for components of this work
   owned by others than the Canadian Government must be honored. To
   copy otherwise, distribute, republish, or post, requires prior
   specific permission and/or a fee. Request permissions from
   owner\hspace*{.5pt}/author(s). 
  \or % cagovmixed
   ACM acknowledges that this contribution was co-authored by an
   affiliate of the national government of Canada. As such, the Crown
   in Right of Canada retains an equal interest in the
   copyright. Reprints must include clear attribution to ACM and the
   author’s government agency affiliation. Permission to make digital
   or hard copies for personal or classroom use is granted. Copies
   must bear this notice and the full citation on the first
   page. Copyrights for components of this work owned by others than
   ACM must be honored. To copy otherwise, distribute, republish, or
   post, requires prior specific permission and/or a fee. Request
   permissions from owner\hspace*{.5pt}/author(s). 
  \or % licensedusgovmixed
   Publication rights licensed to ACM\@. ACM acknowledges that this
   contribution was authored or co-authored by an employee, contractor
   or affiliate of the United States government. As such, the
   Government retains a nonexclusive, royalty-free right to publish or
   reproduce this article, or to allow others to do so, for Government
   purposes only. Request permissions from
   owner\hspace*{.5pt}/author(s). 
  \or % licensedcagov
   This article was authored by employees of the Government of
   Canada. As such, the Canadian government retains all interest in
   the copyright to this work and grants to ACM a nonexclusive,
   royalty-free right to publish or reproduce this article, or to
   allow others to do so, provided that clear attribution is given
   both to the authors and the Canadian government agency employing
   them. Permission to make digital or hard copies for personal or
   classroom use is granted. Copies must bear this notice and the full
   citation on the first page. Copyrights for components of this work
   owned by others than the Canadian Government must be honored. To
   copy otherwise, distribute, republish, or post, requires prior
   specific permission and/or a fee. Request permissions from
   owner\hspace*{.5pt}/author(s). 
  \or % licensedcagovmixed
   Publication rights licensed to ACM. ACM acknowledges that this
   contribution was authored or co-authored by an employee, contractor
   or affiliate of the national government of Canada. As such, the
   Government retains a nonexclusive, royalty-free right to publish or
   reproduce this article, or to allow others to do so, for Government
   purposes only. Request permissions from
   owner\hspace*{.5pt}/author(s). 
  \or % othergov
   ACM acknowledges that this contribution was authored or co-authored
   by an employee, contractor or affiliate of a national
   government. As such, the Government retains a nonexclusive,
   royalty-free right to publish or reproduce this article, or to
   allow others to do so, for Government purposes only. Request
   permissions from owner\hspace*{.5pt}/author(s). 
  \or % licensedothergov
   Publication rights licensed to ACM\@. ACM acknowledges that this
   contribution was authored or co-authored by an employee, contractor
   or affiliate of a national government. As such, the Government
   retains a nonexclusive, royalty-free right to publish or reproduce
   this article, or to allow others to do so, for Government purposes
   only. Request permissions from owner\hspace*{.5pt}/author(s). 
 \or % iw3c2w3
   This paper is published under the Creative Commons Attribution~4.0
   International (CC-BY~4.0) license. Authors reserve their rights to
   disseminate the work on their personal and corporate Web sites with
   the appropriate attribution.
 \or % iw3c2w3g
   This paper is published under the Creative Commons
   Attribution-NonCommercial-NoDerivs~4.0 International
   (CC-BY-NC-ND~4.0) license. Authors reserve their rights to
   disseminate the work on their personal and corporate Web sites with
   the appropriate attribution.
 \or % CC
   \IfEq{\ACM@cc@type}{zero}{%
     \def\ACM@CC@Url{https://creativecommons.org/publicdomain/zero/1.0}}{%
  \edef\ACM@CC@Url{https://creativecommons.org/licenses/\ACM@cc@type/\ACM@cc@version}}%
   \href{\ACM@CC@Url}{\includegraphics[height=5ex]{doclicense-CC-\ACM@cc@type-88x31}}\\
   \href{\ACM@CC@Url}{%
   This work is licensed under a Creative Commons
   \IfEq{\ACM@cc@type}{zero}{CC0 1.0 Universal}{%
     \IfEq{\ACM@cc@type}{by}{Attribution}{}%
     \IfEq{\ACM@cc@type}{by-sa}{Attribution-ShareAlike}{}%
     \IfEq{\ACM@cc@type}{by-nd}{Attribution-NoDerivatives}{}%
     \IfEq{\ACM@cc@type}{by-nc}{Attribution-NonCommercial}{}%
     \IfEq{\ACM@cc@type}{by-nc-sa}{Attribution-NonCommercial-ShareAlike}{}%
     \IfEq{\ACM@cc@type}{by-nc-nd}{Attribution-NonCommercial-NoDerivatives}{}%
     ~\IfEq{\ACM@cc@version}{4.0}{4.0 International}{3.0 Unported}%
   }
   License.}%
 \fi}
%    \end{macrocode}
%
% \end{macro}
%
%%
%
% \begin{macro}{\copyrightyear}
%   By default, the copyright year is the same as \cs{acmYear}, but
%   one can override this:
%    \begin{macrocode}
\def\copyrightyear#1{\def\@copyrightyear{#1}}
\copyrightyear{\@acmYear}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@teaserfigures}
%   The teaser figures container
%    \begin{macrocode}
\def\@teaserfigures{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{teaserfigure}
%   The teaser figure
%    \begin{macrocode}
\newenvironment{teaserfigure}{\Collect@Body\@saveteaser}{}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@saveteaser}
%   Saving the teaser
%    \begin{macrocode}
\long\def\@saveteaser#1{\g@addto@macro\@teaserfigures{\@teaser{#1}}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\thanks}
%   We redefine |amsart| \cs{thanks} so the |anonymous| key works
%    \begin{macrocode}
\renewcommand{\thanks}[1]{%
  \@ifnotempty{#1}{%
    \if@ACM@anonymous
      \g@addto@macro\thankses{\thanks{A note}}%
   \else
    \g@addto@macro\thankses{\thanks{#1}}%
   \fi}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\anon}
% \changes{v1.82}{2022/01/11}{Introduced macro}%
% We provide \cs{anon} command, which obscures parts of the text
%   if the package option |anonymous| is set
%    \begin{macrocode}
\newcommand{\anon}[2][ANONYMIZED]{%
  \if@ACM@anonymous%
    {\color{ACMOrange}#1}%
  \else%
    #2%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% 
%\subsection{Maketitle hook}
%\label{sec:hook}
%
% The current \LaTeX\ provides a nice |lthooks| mechanism.  However,
% since it is relatively new, we will use oldfashioned approach---at
% least for now.
%
% \begin{macro}{\@beginmaketitlehook}
% \changes{v1.75}{2020/11/15}{Introduced macro}
% The hook
%    \begin{macrocode}
\ifx\@beginmaketitlehook\@undefined
  \let\@beginmaketitlehook\@empty
\fi
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\AtBeginMaketitle}
% \changes{v1.75}{2020/11/15}{Introduced macro}
% Adding to the hook
%    \begin{macrocode}
\def\AtBeginMaketitle{\g@addto@macro\@beginmaketitlehook}
%    \end{macrocode}
% \end{macro}
%
%
%\subsection{ACM Engage top matter}
%\label{sec:acmengagetop}
%
% \begin{macro}{\@acmengagemetadata}
% \changes{v1.85}{2022/05/08}{Introduced macro}
% The special metadata for ACM engage course materials
%    \begin{macrocode}
\def\@acmengagemetadata{}
%    \end{macrocode}
% 
% \end{macro}
%
% \begin{macro}{\setengagemetadata}
% \changes{v1.85}{2022/05/08}{Introduced macro}
% Adding topic to engage metadata
%    \begin{macrocode}
\def\setengagemetadata#1#2{%
  \g@addto@macro{\@acmengagemetadata}{%
    \@setengagemetadata{#1}{#2}}}
%    \end{macrocode}
% 
% \end{macro}
%
% \subsection{Typesetting top matter}
% \label{sec:maketitle}
%
% \begin{macro}{\mktitle@bx}
%   Some of our formats use a two-column design.  Some use a one-column
%   design.  In all cases we use a wide title.  Thus we typeset the top
%   matter in a special box to be used in the construction
%   \cs{@twocolumn}\oarg{box}.
%    \begin{macrocode}
\newbox\mktitle@bx
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\maketitle}
% \changes{v1.03}{2016/04/22}{Added special case of authorversion}
% \changes{v1.07}{2016/05/03}{Corrected a bug with abstract footnotes}
% \changes{v1.07}{2016/05/03}{Corrected a bug with permssion and
% footnotes order}
% \changes{v1.10}{2016/05/23}{Corrected a bug with doi in manuscript
% and author vertsion,
% \url{https://github.com/borisveytsman/acmart/issues/36}}
% \changes{v1.12}{2016/05/30}{Moved thankses to copyrightpermission box}
% \changes{v1.33}{2017/03/27}{Fixed the bug with figures on top and
% bottom of the title page, thanks to David Epstein}
% \changes{v1.34}{2017/04/09}{Deleted `DOI' from doi numbers}
% \changes{v1.34}{2017/04/09}{Added date to the bibstrip in conf proceedings}
% \changes{v1.34}{2017/04/09}{The \cs{terms} command is now obsolete}
% \changes{v1.34}{2017/04/11}{Rearranged bibstrip}
% \changes{v1.44}{2017/07/30}{Empty DOI or ISBN are not printed (by
% Michael Ekstrand)}
% \changes{v1.46}{2017/08/27}{Added authors' addresses}
% \changes{v1.46}{2017/08/28}{Thankses go before authors' addresses}
% \changes{v1.48}{2017/09/23}{Added acmart and version info to
% pdfcreator tag}
% \changes{v1.48}{2017/10/14}{Suppressed empty DOI (Serguei Mokhov)}
% \changes{v1.48}{2017/11/18}{Added language and doctitle attributes
% to PDF (Andreas Reichinger)}
% \changes{v1.55}{2018/10/14}{Fixes topnum}
% \changes{v1.58}{2019/01/23}{Original \cs{vspace} inside
% \cs{maketitle}}
% \changes{v1.64}{2019/08/17}{Added a switch setting to show that
% \cs{maketitle} is typeset}
% \changes{v1.72}{2020/06/14}{Do not andify authors for pdf metadata
% (Scott Pakin)}
% \changes{v1.73}{2020/09/07}{Do not check again the presense of
% address fields}
% \changes{v1.75}{2020/11/15}{Added \cs{@beginmaketitlehook}}
% \changes{v1.76}{2021/04/05}{Put \cs{par} inside group for keywords}
% \changes{v1.83}{2022/02/20}{I13n}
% \changes{v1.85}{2022/05/08}{acmengage}
% \changes{v1.87}{2022/07/30}{CC license are allowed for non-acm
% publications}
% \changes{v2.00}{2023/10/24}{Deleted acmPrice}
% \changes{v2.00}{2023/11/05}{Added ``ACM'' for papers}
% \changes{v2.03}{2023/12/04}{Special processing for conferences in
% TOG}
% \changes{v2.11}{2024/12/07}{Deleted conference date from bibstrip}
%   The (in)famous \cs{maketitle}.  Note that in |sigchi-a| mode, authors
%   are \emph{not} in the title box.
%
%  Another note: there is a subtle difference between author notes,
%  title notes and thanks.  The latter two refer to the paper itself
%  and therefore belong to the copyright/permission block.  By the
%  way, this was the default behavior of the old ACM classes.
%    \begin{macrocode}
\def\maketitle{\@beginmaketitlehook
  \@ACM@maketitle@typesettrue
  \if@ACM@anonymous
    % Anonymize omission of \author-s
    \ifnum\num@authorgroups=0\author{}\fi
  \fi
  \begingroup
  \let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig
  \let\@footnotemark\@footnotemark@nolink
  \let\@footnotetext\@footnotetext@nolink
  \renewcommand\thefootnote{\@fnsymbol\c@footnote}%
  \hsize=\textwidth
  \def\@makefnmark{\hbox{\@textsuperscript{\@thefnmark}}}%
  \@mktitle\if@ACM@sigchiamode\else\@mkauthors\fi\@mkteasers
  \@printtopmatter
  \if@ACM@sigchiamode\@mkauthors\fi
  \setcounter{footnote}{0}%
  \def\@makefnmark{\hbox{\@textsuperscript{\normalfont\@thefnmark}}}%
  \@titlenotes
  \@subtitlenotes
  \@authornotes
  \let\@makefnmark\relax
  \let\@thefnmark\relax
  \let\@makefntext\noindent
  \ifx\@empty\thankses\else
    \footnotetextauthorsaddresses{%
      \def\par{\let\par\@par}\parindent\z@\@setthanks}%
  \fi
  \if@ACM@acmcp\else
    \ifx\@empty\@authorsaddresses\else
       \if@ACM@anonymous\else
         \if@ACM@journal@bibstrip@or@tog
           \footnotetextauthorsaddresses{%
             \def\par{\let\par\@par}\parindent\z@\@setauthorsaddresses}%
         \fi
       \fi
    \fi
  \fi
  \if@ACM@nonacm
    \ifnum\acm@copyrightmode=15\relax % cc
       \footnotetextcopyrightpermission{\@copyrightpermission}%
    \fi
  \else
    \if@ACM@acmcp\else
    \footnotetextcopyrightpermission{%
    \if@ACM@authordraft
        \raisebox{-2ex}[\z@][\z@]{\makebox[0pt][l]{\large\bfseries
            Unpublished working draft. Not for distribution.}}%
       \color[gray]{0.9}%
    \fi
    \parindent\z@\parskip0.1\baselineskip
    \if@ACM@authorversion\else
      \if@printpermission\@copyrightpermission\par\fi
    \fi
    \if@ACM@manuscript\else
       \if@ACM@journal@bibstrip\else % Print the conference information
         \if@ACM@engage
            {\itshape \@acmBooktitle, \@acmYear.}\par
         \else 
         {\itshape \acmConference@shortname, \acmConference@venue}\par
         \fi
       \fi
    \fi
    \if@printcopyright
      \copyright\ \@copyrightyear\ \@copyrightowner\\
    \else
      \ifx\@copyrightyear\@empty\else  
        \@copyrightyear.\
      \fi
    \fi
    \if@ACM@manuscript
      Manuscript submitted to ACM\\
    \else
      \if@ACM@authorversion
          This is the author's version of the work. It is posted here for
          your personal use. Not for redistribution. The definitive Version
          of Record was published in
          \if@ACM@journal@bibstrip
            \emph{\@journalName}%
          \else
            \emph{\@acmBooktitle}%
          \fi
          \ifx\@acmDOI\@empty
          .
          \else
            , \@formatdoi{\@acmDOI}.
          \fi\\
        \else
          \if@ACM@nonacm\else
            \if@ACM@journal@bibstrip
              ACM~\@permissionCodeTwo/\@acmYear/\@acmMonth-ART\@acmArticle\\
              \@formatdoi{\@acmDOI}%
            \else % Conference
              \ifx\@acmISBN\@empty\else ACM~ISBN~\@acmISBN\\\fi
              \ifx\@acmDOI\@empty\else\@formatdoi{\@acmDOI}\fi%
            \fi
          \fi
        \fi
      \fi}%
    \fi
    \fi
  \endgroup
  \if@ACM@engage\@typesetengagemetadata\fi  
  \setcounter{footnote}{0}%
  \@mkabstract
  \ifx\@translatedabstracts\@empty\else
  \@translatedabstracts\fi
  \if@ACM@printccs
  \ifx\@concepts\@empty\else\bgroup
      {\@specialsection{CCS Concepts}%
         \noindent\@concepts\par}\egroup
     \fi
   \fi
   \if@ACM@acmcp\else
     \ifx\@keywords\@empty\else\bgroup
        {\@specialsection{\keywordsname}%
           \noindent\@keywords\par}\egroup
     \fi
    \ifx\@translatedkeywords\@empty\else
       \@translatedkeywords
    \fi
  \fi
  \let\metadata@authors=\authors
  \nxandlist{, }{, }{, }\metadata@authors
  \def\@ACM@checkaffil{}%
  \hypersetup{%
    pdfauthor={\metadata@authors},
    pdftitle={\@title},
    pdfsubject={\@concepts},
    pdfkeywords={\@keywords},
    pdfcreator={LaTeX with acmart
      \csname ver@\@classname.cls\endcsname\space
      and hyperref
      \csname <EMAIL>\endcsname}}%
  \andify\authors
  \andify\shortauthors
  \global\let\authors=\authors
  \global\let\shortauthors=\shortauthors
  \if@ACM@printacmref
     \@mkbibcitation
  \fi
  \global\@topnum\z@ % this prevents floats from falling
                     % at the top of page 1
  \global\@botnum\z@ % we do not want them to be on the bottom either
  \@printendtopmatter
  \@afterindentfalse
  \@afterheading
  \if@ACM@acmcp
    \set@ACM@acmcpbox
    \AtEndDocument{\end@ACM@color@frame}%
    \@ACM@color@frame  
  \fi
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\set@ACM@acmcpbox}
% \changes{v1.87}{2022/08/14}{Added macro}
% \changes{v1.89}{2022/12/25}{Added logo}
% \changes{v1.89}{2022/12/25}{Added zref position of the bottom}
% Setting infobox for acmcp
%    \begin{macrocode}
\newbox\@ACM@acmcpbox
\def\set@ACM@acmcpbox{%
  \bgroup
  \hsize=5pc
  \global\setbox\@ACM@acmcpbox=\vbox{%
    \setlength{\parindent}{\z@}%
    {\includegraphics[width=\hsize]{acm-jdslogo}\par}%
    \scriptsize
    \ifnum\getrefnumber{TotPages}>1\else
      \zrefused{@ACM@acmcpbox@y}%
      \zrefused{@ACM@acmcpframe@y}%
      \@tempdima=\dimexpr\zposy{@ACM@acmcpbox@y}sp -
      \zposy{@ACM@acmcpframe@y}sp+0.3\FrameSep+
      \@ACM@acmcp@delta\relax
      \ifdim\@tempdima>0pt\relax         
         \vspace*{\@tempdima}%
         \protected@write\@auxout{}%
         {\string\global\@ACM@acmcp@delta=\the\@tempdima\relax}%
      \fi
    \fi
    \ifx\@acmCodeDataLink\@empty\else\bigskip
    Code and data links:\\ \@acmCodeDataLink\par\bigskip
    \fi
    \ifx\@keywords\@empty\else\bigskip
      Keywords: \@keywords\par
    \fi
    \ifx\@acmContributions\@empty\else\bigskip
      \@acmContributions\par
    \fi
    \ifx\@empty\@authorsaddresses\else\bigskip\@setauthorsaddresses\fi
    \zsaveposy{@ACM@acmcpbox@y}%
    \par
  }
\egroup}
%    \end{macrocode}
% 
% \end{macro}
%
%
% \begin{macro}{\@specialsection}
% \changes{v1.66}{2019/11/23}{Suppress warnings about \cs{vspace}}
% \changes{v1.67}{2020/01/11}{Paragraph inside group does not print
% its argument.  Added a bugfix}
% \changes{v1.68}{2020/01/19}{Deleted grouping altogether}
% \changes{v1.69}{2020/02/02}{Changed dot to colon for sigplan}
% \changes{v1.78}{2021/05/02}{Again suppressed \cs{vspace} warning}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% This macro starts sections for proceedings and uses \cs{small} for journals
%    \begin{macrocode}
\def\@specialsection#1{%
  \let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig
  \ifcase\ACM@format@nr
  \relax % manuscript
    \par\medskip\small\noindent#1: %
  \or % acmsmall
    \par\medskip\small\noindent#1: %
  \or % acmlarge
    \par\medskip\small\noindent#1: %
  \or % acmtog
    \par\medskip\small\noindent#1: %
  \or % sigconf
    \section*{#1}%
  \or % siggraph
    \section*{#1}%
  \or % sigplan
     \noindentparagraph*{#1:~}%
  \or % sigchi
    \section*{#1}%
  \or % sigchi-a
    \section*{#1}%
  \or % acmengage
    \section*{#1}%
  \or % acmcp
    \section*{#1}%
    \fi
  \let\@vspace\@vspace@acm
  \let\@vspacer\@vspacer@acm
}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@printtopmatter}
% \changes{v1.06}{2016/05/01}{Added processing badges}
% \changes{v1.46}{2017/08/29}{Deleted rule}
% \changes{v1.78}{2021/05/16}{Added processing the overflowing title box}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% \changes{1.89}{2022/12/27}{Moved badges to top header}
% The printing of top matter starts a new page and uses the given
%   title box.  
%
% Note that if there are too many authors, |\mktitle@bx| might
% overflow the page.  Therefore we start with checking this and if
% this happens, we split the box and print it page by page.
%    \begin{macrocode}
\def\@printtopmatter{%
  \ifx\@startPage\@empty
     \gdef\@startPage{1}%
  \else
     \setcounter{page}{\@startPage}%
  \fi
  \@tempdima=\ht\mktitle@bx
  \advance\@tempdima by \dp\mktitle@bx
  \ifdim\@tempdima>0.9\textheight
    \loop
      \setbox\@tempboxa=\vsplit \mktitle@bx to 0.9\textheight
      \thispagestyle{firstpagestyle}%
      \noindent\unvbox\@tempboxa
      \clearpage
      \@tempdima=\ht\mktitle@bx
      \advance\@tempdima by \dp\mktitle@bx
    \ifdim\@tempdima>0.9\textheight\repeat
  \fi
  \thispagestyle{firstpagestyle}%
  \noindent
  \ifcase\ACM@format@nr
  \relax % manuscript
    \box\mktitle@bx\par
  \or % acmsmall
    \box\mktitle@bx\par
  \or % acmlarge
    \box\mktitle@bx\par
  \or % acmtog
    \twocolumn[\box\mktitle@bx]%
  \or % sigconf
    \twocolumn[\box\mktitle@bx]%
  \or % siggraph
    \twocolumn[\box\mktitle@bx]%
  \or % sigplan
    \twocolumn[\box\mktitle@bx]%
  \or % sigchi
    \twocolumn[\box\mktitle@bx]%
  \or % sigchi-a
    \par\box\mktitle@bx\par\bigskip
  \or % acmengage
    \twocolumn[\box\mktitle@bx]%
  \or % acmcp
     \box\mktitle@bx\par  
  \fi
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mktitle}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% The title of the article
%    \begin{macrocode}
\def\@mktitle{%
  \ifcase\ACM@format@nr
  \relax % manuscript
    \@mktitle@i
  \or % acmsmall
    \@mktitle@i
  \or % acmlarge
    \@mktitle@i
  \or % acmtog
    \@mktitle@i
  \or % sigconf
    \@mktitle@iii
  \or % siggraph
    \@mktitle@iii
  \or % sigplan
    \@mktitle@iii
  \or % sigchi
    \@mktitle@iii
  \or % sigchi-a
    \@mktitle@iv
  \or % acmengage
    \@mktitle@iii
  \or % acmcp
    \@mktitle@i
  \fi
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@titlefont}
% \changes{v1.06}{2016/05/01}{Added macro}
% \changes{v1.45}{2017/08/15}{Switched \cs{bfeseries}\cs{sffamily} to
% \cs{sffamily}\cs{bfseries}}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% The font to typeset the title
%    \begin{macrocode}
\def\@titlefont{%
  \ifcase\ACM@format@nr
  \relax % manuscript
    \LARGE\sffamily\bfseries
  \or % acmsmall
    \LARGE\sffamily\bfseries
  \or % acmlarge
    \LARGE\sffamily\bfseries
  \or % acmtog
    \Huge\sffamily
  \or % sigconf
    \Huge\sffamily\bfseries
  \or % siggraph
    \Huge\sffamily\bfseries
  \or % sigplan
    \Huge\bfseries
  \or % sigchi
    \Huge\sffamily\bfseries
  \or % sigchi-a
     \Huge\bfseries
  \or % acmengage
    \Huge\sffamily\bfseries
  \or % acmcp
    \LARGE\sffamily\bfseries
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@subtitlefont}
% \changes{v1.06}{2016/05/01}{Added macro}
% \changes{v1.33}{2017/03/12}{Added \cs{normalsize}}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% The font to typeset the subtitle
%    \begin{macrocode}
\def\@subtitlefont{\normalsize
  \ifcase\ACM@format@nr
  \relax % manuscript
    \mdseries
  \or % acmsmall
    \mdseries
  \or % acmlarge
    \mdseries
  \or % acmtog
     \LARGE
  \or % sigconf
     \LARGE\mdseries
  \or % siggraph
     \LARGE\mdseries
  \or % sigplan
     \LARGE\mdseries
  \or % sigchi
     \LARGE\mdseries
  \or % sigchi-a
     \mdseries
  \or % acmengage
     \LARGE\mdseries
  \or % acmcp
    \mdseries
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mktitle@i}
% \changes{v1.06}{2016/05/01}{Made generic}
% \changes{v1.06}{2016/05/01}{Added processing badges}
% \changes{v1.83}{2022/02/20}{Added \cs{translatedtitle} and
% \cs{translatedsubtitle}}
% \changes{v1.87}{2022/08/14}{Special treatment for acmcp}
% \changes{v1.88}{2022/10/24}{Moved down title for acmcp}
% \changes{v1.89}{2022/12/27}{Moved badges to top header}
% \changes{v1.89}{2023/01/07}{Moved up acmcp title}
%   The version of \cs{mktitle} for most journals
%    \begin{macrocode}
\def\@mktitle@i{\hsize=\textwidth
  \if@ACM@acmcp
    \advance\hsize by -6pc%
  \fi
  \@ACM@title@width=\hsize
  \setbox\mktitle@bx=\vbox{\noindent\@titlefont
    \parbox[t]{\@ACM@title@width}{\raggedright
      \@titlefont\noindent
      \@title\@translatedtitle%
  \ifx\@subtitle\@empty\else
    \par\noindent{\@subtitlefont\@subtitle\@translatedsubtitle}%
  \fi}%
  \par\bigskip}}%
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mktitle@ii}
% \changes{v1.06}{2016/05/01}{Now this macro is obsolete}
%   The version of \cs{mktitle} for TOG.  Since v1.06, this is subsumed by
%   the \cs{mktitle@i} macro
% \end{macro}
%
%
% \begin{macro}{\@mktitle@iii}
% \changes{v1.06}{2016/05/01}{Made more generic}
% \changes{v1.06}{2016/05/01}{Added processing badges}
% \changes{v1.83}{2022/02/20}{Added \cs{translatedtitle} and
% \cs{translatedsubtitle}} 
% \changes{v1.89}{2022/12/27}{Moved badges to top header}
%   The version of \cs{@mktitle} for SIG proceedings.  
%    \begin{macrocode}
\def\@mktitle@iii{\hsize=\textwidth
    \setbox\mktitle@bx=\vbox{\@titlefont\centering
      \@ACM@title@width=\hsize
      \parbox[t]{\@ACM@title@width}{\centering\@titlefont
        \@title\@translatedtitle%
        \ifx\@subtitle\@empty\else
          \par\noindent{\@subtitlefont\@subtitle\@translatedsubtitle}
        \fi
      }%
      \par\bigskip}}%
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@mktitle@iv}
% \changes{v1.06}{2016/05/01}{Made more generic}
% \changes{v1.83}{2022/02/20}{Added \cs{translatedtitle} and
% \cs{translatedsubtitle}} 
%   The version of \cs{@mktitle} for |sigchi-a|
%    \begin{macrocode}
\def\@mktitle@iv{\hsize=\textwidth
    \setbox\mktitle@bx=\vbox{\raggedright\leftskip5pc\@titlefont
      \noindent\leavevmode\leaders\hrule height 2pt\hfill\kern0pt\par
      \noindent\@title\@translatedtitle%
     \ifx\@subtitle\@empty\else
       \par\noindent\@subtitlefont\@subtitle\@translatedsubtitle%
     \fi
     \par\bigskip}}%
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@ACM@addtoaddress}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
%   This macro adds an item to the address using the following rules:
%   \begin{enumerate}
%   \item If we start a paragraph, add the item
%   \item Otherwise, add a comma and the item
%   \item However, the comma is deleted if it is at the end of a
%   line.  We use the magic \cs{cleaders} trick for this.
%   \end{enumerate}
%    \begin{macrocode}
\newbox\@ACM@commabox
\def\@ACM@addtoaddress#1{%
  \ifvmode\else
    \if@ACM@affiliation@obeypunctuation\else
    \setbox\@ACM@commabox=\hbox{, }%
    \unskip\cleaders\copy\@ACM@commabox\hskip\wd\@ACM@commabox
  \fi\fi
  #1}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\institution}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.73}{2020/09/07}{Check the presence in affil}
% \begin{macro}{\position}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \begin{macro}{\department}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.30}{2017/02/10}{Added optional parameter}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \begin{macro}{\streetaddress}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.40}{2017/06/15}{We now do not print this even in SIG}
% \changes{v2.06}{2024/04/13}{We now do not collect postal information
% anymore}
% \begin{macro}{\city}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.40}{2017/06/15}{We now do not print this even in SIG}
% \changes{v1.73}{2020/09/07}{Check the presence in affil}
% \begin{macro}{\state}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.40}{2017/06/15}{We now do not print this even in SIG}
% \begin{macro}{\postcode}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.40}{2017/06/15}{We now do not print this even in SIG}
% \changes{v2.06}{2024/04/13}{We now do not collect postal information
% anymore}
% \begin{macro}{\country}
% \changes{v1.15}{2016/07/03}{Added macro}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.40}{2017/05/27}{Fixed bugs with extra spaces}
% \changes{v1.43}{2017/07/11}{Added comma before country for journals}
% \changes{v1.46}{2017/08/30}{Corrected spacing for institution}
% \changes{v1.73}{2020/09/07}{Check the presence in affil}
% \changes{v1.86}{2022/06/26}{Error if country is empty}
%   Theoretically we can define the macros for \cs{affiliation} inside
%   the \cs{@mkauthors}-style commands.  However, this would lead to a
%   strange error if an author uses them outside \cs{affiliation}.  Of
%   course we can make them produce an error message, but\ldots
%    \begin{macrocode}
\def\streetaddress#1{\ClassWarning{\@classname}{ACM no longer collects
  authors' postal addresses.  I am ignoring your street
  address}\unskip\ignorespaces} 
\def\postcode#1{\ClassWarning{\@classname}{ACM no longer collects
  authors' postal addresses.  I am ignoring your postal
  code}\unskip\ignorespaces} 
\if@ACM@journal
  \def\position#1{\unskip\ignorespaces}
  \def\institution#1{\global\@ACM@instpresenttrue
    \unskip~#1\ignorespaces}
  \def\city#1{\global\@ACM@citypresenttrue\unskip\ignorespaces}
  \def\state#1{\unskip\ignorespaces}
  \newcommand\department[2][0]{\unskip\ignorespaces}
  \def\country#1{\StrDel{#1}{ }[\@tempa]%
    \ifx\@tempa\@empty\else
    \global\@ACM@countrypresenttrue\fi
    \if@ACM@affiliation@obeypunctuation\else, \fi#1\ignorespaces}
\else
  \def\position#1{\if@ACM@affiliation@obeypunctuation#1\else#1\par\fi}%
  \def\institution#1{\global\@ACM@instpresenttrue
    \if@ACM@affiliation@obeypunctuation#1\else#1\par\fi}%
  \newcommand\department[2][0]{\if@ACM@affiliation@obeypunctuation
    #2\else#2\par\fi}%
  \def\city#1{\global\@ACM@citypresenttrue\@ACM@addtoaddress{#1}}%
  \let\state\@ACM@addtoaddress
  \def\country#1{\global\@ACM@countrypresenttrue\@ACM@addtoaddress{#1}}%
\fi
%    \end{macrocode}
%
% \end{macro}
% \end{macro}
% \end{macro}
% \end{macro}
% \end{macro}
% \end{macro}
% \end{macro}
% \end{macro}
%
% \begin{macro}{\@mkauthors}
% \changes{v1.17}{2016/07/09}{TOG now uses the same authors block as
% other journals}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% Typesetting the authors
%    \begin{macrocode}
\def\@mkauthors{\begingroup
  \hsize=\textwidth
  \ifcase\ACM@format@nr
  \relax % manuscript
    \@mkauthors@i
  \or % acmsmall
    \@mkauthors@i
  \or % acmlarge
    \@mkauthors@i
  \or % acmtog
    \@mkauthors@i
  \or % sigconf
    \@mkauthors@iii
  \or % siggraph
    \@mkauthors@iii
  \or % sigplan
    \@mkauthors@iii
  \or % sigchi
    \@mkauthors@iii
  \or % sigchi-a
    \@mkauthors@iv
  \or % acmengage
    \@mkauthors@iii
  \or % acmcp
    \@mkauthors@i
  \fi
  \endgroup
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@authorfont}
%   Somehow different conferences use different fonts for author
%   names.  Why?
%    \begin{macrocode}
\def\@authorfont{\Large\sffamily}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@affiliationfont}
%   Font for affiliations
%    \begin{macrocode}
\def\@affiliationfont{\normalsize\normalfont}
%    \end{macrocode}
% \end{macro}
%
% \changes{v1.13}{2016/06/06}{Font adjustments for acmsmall}
% Adjusting fonts for different formats
%    \begin{macrocode}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
  \def\@authorfont{\large\sffamily}
  \def\@affiliationfont{\small\normalfont}
\or % acmlarge
\or % acmtog
  \def\@authorfont{\LARGE\sffamily}
  \def\@affiliationfont{\large}
\or % sigconf
  \def\@authorfont{\LARGE}
  \def\@affiliationfont{\large}
\or % siggraph
  \def\@authorfont{\normalsize\normalfont}
  \def\@affiliationfont{\normalsize\normalfont}
\or % sigplan
  \def\@authorfont{\Large\normalfont}
  \def\@affiliationfont{\normalsize\normalfont}
\or % sigchi
  \def\@authorfont{\bfseries}
  \def\@affiliationfont{\mdseries}
\or % sigchi-a
  \def\@authorfont{\bfseries}
  \def\@affiliationfont{\mdseries}
\or % acmengage
  \def\@authorfont{\LARGE}
  \def\@affiliationfont{\large}
\or % acmcp
  \def\@authorfont{\large\sffamily}
  \def\@affiliationfont{\small\normalfont}
\fi
%    \end{macrocode}
%
% \begin{macro}{\@typeset@author@line}
% \changes{v1.18}{2016/07/12}{Added macro}
%   At this point we have \cs{@currentauthors} and
%   \cs{@currentaffiliations}.  We typeset them in the journal style
%    \begin{macrocode}
\def\@typeset@author@line{%
  \andify\@currentauthors\par\noindent
  \@currentauthors\def\@currentauthors{}%
  \ifx\@currentaffiliations\@empty\else
    \andify\@currentaffiliations
      \unskip, {\@currentaffiliations}\par
  \fi
  \def\@currentaffiliations{}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\if@ACM@instpresent}
% \changes{v1.73}{2020/09/07}{Added macro}
% Whether the given affiliation has institution
%    \begin{macrocode}
\newif\if@ACM@instpresent
\@ACM@instpresenttrue
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\if@ACM@citypresent}
% \changes{v1.73}{2020/09/07}{Added macro}
% Whether the given affiliation has city
%    \begin{macrocode}
\newif\if@ACM@citypresent
\@ACM@citypresenttrue
%    \end{macrocode}
%
% \end{macro}
% \begin{macro}{\if@ACM@countrypresent}
% \changes{v1.73}{2020/09/07}{Added macro}
% Whether the given affiliation has country
%    \begin{macrocode}
\newif\if@ACM@countrypresent
\@ACM@countrypresenttrue
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@ACM@resetaffil}
% \changes{v1.73}{2020/09/07}{Added macro}
% Reset affiliation flags
%    \begin{macrocode}
\def\@ACM@resetaffil{%
  \global\@ACM@instpresentfalse
  \global\@ACM@citypresentfalse
  \global\@ACM@countrypresentfalse
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@ACM@checkaffil}
% \changes{v1.73}{2020/09/07}{Added macro}
% \changes{v1.75}{2020/11/15}{Changed warning to error for country}
% Check affiliation flags
%    \begin{macrocode}
\def\@ACM@checkaffil{%
  \if@ACM@instpresent\else
  \ClassWarningNoLine{\@classname}{No institution present for an affiliation}%
  \fi
  \if@ACM@citypresent\else
  \ClassWarningNoLine{\@classname}{No city present for an affiliation}%
  \fi
  \if@ACM@countrypresent\else
  \ClassError{\@classname}{No country present for an affiliation}{ACM
    requires each author to indicate their country using country macro.}%
  \fi
}
%    \end{macrocode}
%
% \end{macro}
%
%
%
%
% \begin{macro}{\@mkauthors@i}
% \changes{v1.18}{2016/07/12}{Now we andify affiliations}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.40}{2017/06/04}{Switched to MakeTextUppercase}
% \changes{v1.73}{2020/09/07}{Check the presence of required elements
% for affiliations}
% \changes{v1.78}{2021/05/16}{Unboxing \cs{mktitle@bx}}
%   This version is used in most journal formats.  Note that \cs{and} between
%   authors with the same affiliation becomes \verb*| and |:
% \changes{v1.87}{2022/08/14}{Special treatment for acmcp}
% \changes{v1.12}{2024/12/28}{Removed MakeTextUppercase due to a bug
% with new kernel}
%    \begin{macrocode}
\def\@mkauthors@i{%
  \def\@currentauthors{}%
  \def\@currentaffiliations{}%
  \global\let\and\@typeset@author@line
  \def\@author##1{%
    \ifx\@currentauthors\@empty
      \gdef\@currentauthors{\@authorfont\MakeUppercase{##1}}%
    \else
       \g@addto@macro{\@currentauthors}{\and\MakeUppercase{##1}}%
    \fi
    \gdef\and{}}%
  \def\email##1##2{}%
  \def\affiliation##1##2{%
    \def\@tempa{##2}\ifx\@tempa\@empty\else
       \ifx\@currentaffiliations\@empty
          \gdef\@currentaffiliations{%
            \setkeys{@ACM@affiliation@}{obeypunctuation=false}%
            \setkeys{@ACM@affiliation@}{##1}%
            \@ACM@resetaffil
            \@affiliationfont##2\@ACM@checkaffil}%
       \else
         \g@addto@macro{\@currentaffiliations}{\and
           \setkeys{@ACM@affiliation@}{obeypunctuation=false}%
           \setkeys{@ACM@affiliation@}{##1}\@ACM@resetaffil
           ##2\@ACM@checkaffil}%
      \fi
    \fi
     \global\let\and\@typeset@author@line}%
  \if@ACM@acmcp
    \advance\hsize by -6pc%
  \fi
  \global\setbox\mktitle@bx=\vbox{\noindent\unvbox\mktitle@bx\par\medskip
    \noindent\addresses\@typeset@author@line
   \par\medskip}%
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mkauthors@ii}
% \changes{v1.17}{2016/07/09}{Deleted}
%   The \cs{@mkauthors@ii} command was the version used in |acmtog|.
%   It is no longer necessary.
%
% \end{macro}
%
% \begin{macro}{\author@bx}
%   The box to put an individual author in
%    \begin{macrocode}
\newbox\author@bx
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\author@bx@wd}
%   The width of the author box
%    \begin{macrocode}
\newdimen\author@bx@wd
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\author@bx@sep}
%   The separation between author boxes
%    \begin{macrocode}
\newskip\author@bx@sep
\author@bx@sep=1pc\relax
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@typeset@author@bx}
% \changes{v1.15}{2016/07/04}{Moved to separate macro}
% \changes{v1.61}{2019/05/26}{Added \cs{normalbaselines}}
%   Typesetting the box with authors.  Note that in |sigchi-a| the box
%   is not centered.
%    \begin{macrocode}
\def\@typeset@author@bx{\bgroup\hsize=\author@bx@wd
  \def\and{\par}\normalbaselines
  \global\setbox\author@bx=\vtop{\if@ACM@sigchiamode\else\centering\fi
    \@authorfont\@currentauthors\par\@affiliationfont
    \@currentaffiliation}\egroup
  \box\author@bx\hspace{\author@bx@sep}%
  \gdef\@currentauthors{}%
  \gdef\@currentaffiliation{}}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@mkauthors@iii}
% \changes{v1.15}{2016/07/04}{New authors system}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.36}{2017/05/12}{Added authorsperrow overrride}
% \changes{v1.73}{2020/09/07}{Check the presence of required elements
% for affiliations}
% \changes{v1.78}{2021/05/16}{Unboxing \cs{mktitle@bx}}
%   The |sigconf| version.  Here we use a centered design with each
%   author in a separate box.
%    \begin{macrocode}
\def\@mkauthors@iii{%
%    \end{macrocode}
% First, we need to determine the design of the author strip.  The
% boxes are separated by \cs{author@bx@sep} plus two
% \cs{author@bx@sep} margins.  This means that each box must be of
% width $(\cs{textwidth}-\cs{author@bx@sep})/N-\cs{author@bx@sep}$,
% where $N$ is the number of boxes per row.
%    \begin{macrocode}
  \author@bx@wd=\textwidth\relax
  \advance\author@bx@wd by -\author@bx@sep\relax
  \ifnum\@ACM@authorsperrow>0\relax
    \divide\author@bx@wd by \@ACM@authorsperrow\relax
  \else
    \ifcase\num@authorgroups
    \relax % 0?
    \or  % 1=one author per row
    \or  % 2=two authors per row
       \divide\author@bx@wd by \num@authorgroups\relax
    \or  % 3=three authors per row
       \divide\author@bx@wd by \num@authorgroups\relax
    \or  % 4=two authors per row (!)
       \divide\author@bx@wd by 2\relax
    \else % three authors per row
       \divide\author@bx@wd by 3\relax
    \fi
  \fi
  \advance\author@bx@wd by -\author@bx@sep\relax
%    \end{macrocode}
% Now, parsing of \cs{addresses}:
%    \begin{macrocode}
  \gdef\@currentauthors{}%
  \gdef\@currentaffiliation{}%
  \def\@author##1{\ifx\@currentauthors\@empty
    \gdef\@currentauthors{\par##1}%
  \else
    \g@addto@macro\@currentauthors{\par##1}%
  \fi
  \gdef\and{}}%
  \def\email##1##2{\ifx\@currentaffiliation\@empty
    \gdef\@currentaffiliation{\bgroup
      \mathchardef\UrlBreakPenalty=10000\nolinkurl{##2}\egroup}%
  \else
    \g@addto@macro\@currentaffiliation{\par\bgroup
      \mathchardef\UrlBreakPenalty=10000\nolinkurl{##2}\egroup}%
  \fi}%
  \def\affiliation##1##2{\ifx\@currentaffiliation\@empty
    \gdef\@currentaffiliation{%
      \setkeys{@ACM@affiliation@}{obeypunctuation=false}%
      \setkeys{@ACM@affiliation@}{##1}\@ACM@resetaffil
      ##2\@ACM@checkaffil}%
  \else
    \g@addto@macro\@currentaffiliation{\par
      \setkeys{@ACM@affiliation@}{obeypunctuation=false}%
      \setkeys{@ACM@affiliation@}{##1}\@ACM@resetaffil
      ##2\@ACM@checkaffil}%
  \fi
  \global\let\and\@typeset@author@bx
}%
%    \end{macrocode}
% Actual typesetting is done by the \cs{and} macro:
%    \begin{macrocode}
  \hsize=\textwidth
  \global\setbox\mktitle@bx=\vbox{\noindent
    \unvbox\mktitle@bx\par\medskip\leavevmode
    \lineskip=1pc\relax\centering\hspace*{-1em}%
    \addresses\let\and\@typeset@author@bx\and\par\bigskip}}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@mkauthors@iv}
% \changes{v1.33}{2017/03/28}{Added obeypunctuation code}
% \changes{v1.36}{2017/05/12}{Added authorsperrow overrride}
% \changes{v1.73}{2020/09/07}{Check the presence of required elements
% for affiliations}
%   The |sigchi-a| version.  We put authors in the main text with
%   no more than 2 authors per line, unless overriden.
%    \begin{macrocode}
\def\@mkauthors@iv{%
%    \end{macrocode}
% First, we need to determine the design of the author strip.  The
% boxes are separated by \cs{author@bx@sep} plus two
% \cs{author@bx@sep} margins.  This means that each box must be of
% width $(\cs{textwidth}-\cs{author@bx@sep})/N-\cs{author@bx@sep}$,
% where $N$ is the number of boxes per row.
%    \begin{macrocode}
  \author@bx@wd=\columnwidth\relax
  \advance\author@bx@wd by -\author@bx@sep\relax
  \ifnum\@ACM@authorsperrow>0\relax
    \divide\author@bx@wd by \@ACM@authorsperrow\relax
  \else
    \ifcase\num@authorgroups
    \relax % 0?
    \or  % 1=one author per row
    \else  % 2=two authors per row
       \divide\author@bx@wd by 2\relax
    \fi
  \fi
  \advance\author@bx@wd by -\author@bx@sep\relax
%    \end{macrocode}
% Now, parsing of \cs{addresses}:
%    \begin{macrocode}
  \gdef\@currentauthors{}%
  \gdef\@currentaffiliation{}%
  \def\@author##1{\ifx\@currentauthors\@empty
    \gdef\@currentauthors{\par##1}%
  \else
    \g@addto@macro\@currentauthors{\par##1}%
  \fi
  \gdef\and{}}%
  \def\email##1##2{\ifx\@currentaffiliation\@empty
    \gdef\@currentaffiliation{\nolinkurl{##2}}%
  \else
    \g@addto@macro\@currentaffiliation{\par\nolinkurl{##2}}%
  \fi}%
  \def\affiliation##1##2{\ifx\@currentaffiliation\@empty
    \gdef\@currentaffiliation{%
      \setkeys{@ACM@affiliation@}{obeypunctuation=false}%
      \setkeys{@ACM@affiliation@}{##1}\@ACM@resetaffil
      ##2\@ACM@checkaffil}%
  \else
    \g@addto@macro\@currentaffiliation{\par
      \setkeys{@ACM@affiliation@}{obeypunctuation=false}%
      \setkeys{@ACM@affiliation@}{##1}\@ACM@resetaffil
      ##2\@ACM@checkaffil}%
  \fi
  \global\let\and\@typeset@author@bx}%
%
%    \end{macrocode}
% Actual typesetting is done by the \cs{and} macro
%    \begin{macrocode}
    \bgroup\hsize=\columnwidth
    \par\raggedright\leftskip=\z@
    \lineskip=1pc\noindent
    \addresses\let\and\@typeset@author@bx\and\par\bigskip\egroup}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mkauthorsaddresses}
% \changes{v1.46}{2017/08/27}{Introduced macro}
% \changes{v1.65}{2019/10/13}{Deleted cs{@addpunct}}
% \changes{v1.77}{2021/04/16}{New separator of institutions}
% \changes{v2.06}{2024/04/13}{\cs{streetaddress} and \cs{postcode} now
% produce warnings}
% Typesetting authors' addresses in the footnote style
%    \begin{macrocode}
\def\@mkauthorsaddresses{%
  \ifnum\num@authors>1\relax
  Authors' \else Author's \fi
  Contact Information:
  \bgroup
  \def\streetaddress##1{\ClassWarning{\@classname}{ACM no longer collects
  authors' postal addresses.  I am ignoring your street
  address}\unskip\ignorespaces}% 
  \def\postcode##1{\ClassWarning{\@classname}{ACM no longer collects
  authors' postal addresses.  I am ignoring your postal
  code}\unskip\ignorespaces}%
  \def\position##1{\unskip\ignorespaces}%
  \gdef\@ACM@institution@separator{, }%
  \def\institution##1{\unskip\@ACM@institution@separator ##1\gdef\@ACM@institution@separator{ and }}%
  \def\city##1{\unskip, ##1}%
  \def\state##1{\unskip, ##1}%
  \renewcommand\department[2][0]{\unskip\@addpunct, ##2}%
  \def\country##1{\unskip, ##1}%
  \def\and{\unskip; \gdef\@ACM@institution@separator{, }}%
  \def\@author##1{##1}%
  \def\email##1##2{\unskip, \nolinkurl{##2}}%
  \addresses
  \egroup}
%    \end{macrocode}
%
% \changes{1.66}{2019/12/18}{Added warning for missing addresses}
%    \begin{macrocode}
\AtEndDocument{\if@ACM@nonacm\else\if@ACM@journal
  \ifx\@authorsaddresses\@empty
  \ClassWarningNoLine{\@classname}{Authors'
    addresses are mandatory for ACM journals}%
  \fi\fi\fi}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\@setaddresses}
%   This is an |amsart| macro that we do not need.
%    \begin{macrocode}
\def\@setaddresses{}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@authornotemark}
% Adding a footnote mark to authors.  This version adds a ``normal''
% footnote mark.
%    \begin{macrocode}
\def\@authornotemark{\g@addto@macro\@currentauthors{\footnotemark\relax}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@@authornotemark}
% \changes{v1.39}{2017/05/14}{Added macro}
% Adding a footnote mark to authors with a given number
%    \begin{macrocode}
\def\@@authornotemark#1{\g@addto@macro\@currentauthors{\footnotemark[#1]}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mkteasers}
% Typesetting the teasers
% \changes{v1.56}{2018/11/1}{Added check for description}
% \changes{v1.78}{2021/05/16}{Unboxing \cs{mktitle@bx}}
%    \begin{macrocode}
\def\@mkteasers{%
  \ifx\@teaserfigures\@empty\else
    \def\@teaser##1{\par\bigskip\bgroup
      \captionsetup{type=figure}##1\egroup\par}
    \global\setbox\mktitle@bx=\vbox{\noindent\unvbox\mktitle@bx\par
      \noindent\@Description@presentfalse
      \@teaserfigures\par\if@Description@present\else
         \global\@undescribed@imagestrue
         \ClassWarning{\@classname}{A possible image without
           description}\fi
    \medskip}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mkabstract}
% \changes{v1.19}{2016/07/28}{Include 'Abstract' in PDF bookmarks
% (Matthew Fluet)}
% \changes{v1.20}{2016/08/03}{Deleted spurious space}
% \changes{v1.29}{2017/01/22}{Removed spurious indentation (John
% Wickerson)}
% \changes{v1.48}{2017/09/16}{Removed spurious indentation if abstract
% is followed by an empty line}
% \changes{v1.83}{2022/02/20}{I13n}
%   Typesetting the abstract
%    \begin{macrocode}
\def\@mkabstract{\bgroup
  \ifx\@abstract\@lempty\else
  {\phantomsection\addcontentsline{toc}{section}{\abstractname}%
    \if@ACM@journal
       \everypar{\setbox\z@\lastbox\everypar{}}\small
    \else
      \section*{\abstractname}%
    \fi
   \ignorespaces\@abstract\par}%
  \fi\egroup}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@mktranslatedabstract}
% \changes{v1.83}{2022/02/20}{Added macro}
%   Typesetting a translated abstract
%    \begin{macrocode}
\def\@mktranslatedabstract#1{\selectlanguage{#1}%
    \if@ACM@journal
       \everypar{\setbox\z@\lastbox\everypar{}}\small
    \else
       \section*{\abstractname}%
    \fi
   \ignorespaces}
%    \end{macrocode}
%
% \end{macro}
%
% 
% \begin{macro}{\@mkbibcitation}
% \changes{v1.17}{2016/07/10}{Changed format for sigs}
% \changes{v1.17}{2016/07/10}{Added \cs{nobreak}}
% \changes{v1.31}{2017/03/04}{Disabled linebreak}
% \changes{v1.34}{2017/04/09}{Deleted DOI from doi numbers}
% \changes{v1.44}{2017/07/30}{If the paper has just one page, use
% `page' instead of `pages'}
% \changes{v1.46}{2017/08/25}{Added subtitle}
% \changes{v1.54}{2018/06/10}{Bug corrected (Ross Moore)}
% \changes{v1.55}{2018/07/25}{Bug corrected}
% \changes{v1.58}{2019/01/23}{Deleted spurious \cs{vspace} warning}
%   Print the |bibcitation| format
%    \begin{macrocode}
\def\@mkbibcitation{\bgroup
  \let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig
  \def\@pages@word{\ifnum\getrefnumber{TotPages}=1\relax page\else pages\fi}%
  \def\footnotemark{}%
  \def\\{\unskip{} \ignorespaces}%
  \def\footnote{\ClassError{\@classname}{Please do not use footnotes
      inside a \string\title{} or \string\author{} command! Use
      \string\titlenote{} or \string\authornote{} instead!}}%
  \def\@article@string{\ifx\@acmArticle\@empty{\ }\else,
    Article~\@acmArticle\ \fi}%
  \par\medskip\small\noindent{\bfseries ACM Reference Format:}\par\nobreak
  \noindent\bgroup
    \def\\{\unskip{}, \ignorespaces}\authors\egroup. \@acmYear. \@title
  \ifx\@subtitle\@empty. \else: \@subtitle. \fi
  \if@ACM@nonacm\else
    % The 'nonacm' option disables 'printacmref' by default,
    % and the present \@mkbibcitation definition is never used
    % in this case. The conditional remains useful if the user
    % explicitly sets \settopmatter{printacmref=true}.
    \if@ACM@journal@bibstrip
       \textit{\@journalNameShort}
       \@acmVolume, \@acmNumber \@article@string (\@acmPubDate),
       \ref{TotPages}~\@pages@word.
    \else
       In \textit{\@acmBooktitle}%
       \ifx\@acmEditors\@empty\textit{.}\else
         \andify\@acmEditors\textit{, }\@acmEditors~\@editorsAbbrev.%
       \fi\
       ACM, New York, NY, USA%
         \@article@string\unskip, \ref{TotPages}~\@pages@word.
    \fi
  \fi
  \ifx\@acmDOI\@empty\else\@formatdoi{\@acmDOI}\fi
\par\egroup}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@printendtopmatter}
% \changes{v1.46}{2017/08/28}{Made it \cs{par}\cs{bigskip} uniformly}
% \changes{v1.58}{2019/01/23}{Deleted spurious \cs{vspace} warning}
% \changes{v1.69}{2020/02/02}{Deleted grouping}
%   End the top matter
%    \begin{macrocode}
\def\@printendtopmatter{%
  \let\@vspace\@vspace@orig
  \let\@vspacer\@vspacer@orig
  \par\bigskip
  \let\@vspace\@vspace@acm
  \let\@vspacer\@vspacer@acm
  }
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@setthanks}
%   We redefine \cs{\@setthanks} using \cs{long}
%    \begin{macrocode}
\def\@setthanks{\long\def\thanks##1{\par##1\@addpunct.}\thankses}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@setauthorsaddresses}
% \changes{v1.46}{2018/08/25}{Introduced macro}
%    \begin{macrocode}
\def\@setauthorsaddresses{\@authorsaddresses\unskip\@addpunct.}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@typesetengagemetadata}
% \changes{v1.85}{2022/05/08}{Introduced macro}
% Typesetting special metadata for ACM Engage
%    \begin{macrocode}
\def\@typesetengagemetadata{%
  \def\@setengagemetadata##1##2{\par\noindent\textbf{##1} ##2\par}%
  \@acmengagemetadata}
%    \end{macrocode}
% 
% \end{macro}
%
%<*tagged>
% We stop tagging inside maketitle for now.  Ulrike's code again.
%    \begin{macrocode}
\ExplSyntaxOn
\AddToHook{cmd/maketitle/before}
 {\tagstructbegin{tag=Title}\tagmcbegin{} \tag_stop:}
\AddToHook{cmd/maketitle/after}
 {\tag_start:\tagmcend\tagstructend }
\ExplSyntaxOff
%    \end{macrocode}
%</tagged>
%
%\subsection{Headers and Footers}
%\label{sec:head_foot}
%
% We use |fancyhdr| for our headers and footers:
%    \begin{macrocode}
\RequirePackage{fancyhdr}
%    \end{macrocode}
%
% \begin{macro}{\ACM@restore@pagestyle}
% \changes{v1.44}{2017/07/30}{Added macro}
% The following code by Ross Moore protects against changes by
% the |totpages| package:
%    \begin{macrocode}
\let\ACM@ps@plain\ps@plain
\let\ACM@ps@myheadings\ps@myheadings
\let\ACM@ps@headings\ps@headings
\def\ACM@restore@pagestyle{%
  \let\ps@plain\ACM@ps@plain
  \let\ps@myheadings\ACM@ps@myheadings
  \let\ps@headings\ACM@ps@headings}
\AtBeginDocument{\ACM@restore@pagestyle}
%    \end{macrocode}
%
%
% \end{macro}
%
%
%
% \begin{macro}{\ACM@linecount@bx}
% \changes{v1.34}{2017/04/10}{Rulers now are continuous}
% \changes{v1.40}{2017/05/27}{Work around a bug in xcolor: looks like
% cmyk colors in boxes do not work}
% \changes{v1.46}{2017/08/28}{Rearranged the code to get rid of
% spurious underfull messages (Benjamin Byholm)}
% \changes{v1.64}{2019/08/24}{Bug fix: made the spacing on the left
% and the right size equal}
%   This is the box displayed in review mode
%    \begin{macrocode}
\if@ACM@review
  \newsavebox{\ACM@linecount@bx}
  \newlength\ACM@linecount@bxht
  \newcount\ACM@linecount
  \ACM@linecount\@ne\relax
  \def\ACM@mk@linecount{%
    \savebox{\ACM@linecount@bx}[4em][t]{\parbox[t]{4em}{\normalfont
        \normalsize
        \setlength{\ACM@linecount@bxht}{0pt}%
        \loop{\color{red}\scriptsize\the\ACM@linecount}\\
        \global\advance\ACM@linecount by \@ne
        \addtolength{\ACM@linecount@bxht}{\baselineskip}%
        \ifdim\ACM@linecount@bxht<\textheight\repeat
        {\color{red}\scriptsize\the\ACM@linecount}\hfill
        \global\advance\ACM@linecount by \@ne}}}
\fi
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\ACM@linecountL}
% \changes{v1.33}{2017/03/29}{Renamed macro}
% \changes{v1.34}{2017/04/10}{Rulers now are continuous}
%   How to display the box on the left
%    \begin{macrocode}
\def\ACM@linecountL{%
  \if@ACM@review
  \ACM@mk@linecount
  \begin{picture}(0,0)%
    \put(-26,-22){\usebox{\ACM@linecount@bx}}%
  \end{picture}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\ACM@linecountR}
% \changes{v1.33}{2017/03/29}{Added macro}
% \changes{v1.34}{2017/04/10}{Rulers now are continuous}
% \changes{v1.69}{2020/02/02}{Do not increase numbers in one column format}
% \changes{1.85}{2022/05/08}{Added: acmengage}
% How to display the box on the right.  In one column formats we do
%   not step the numbers.
%    \begin{macrocode}
\def\ACM@linecountR{%
  \if@ACM@review
    \ifcase\ACM@format@nr
    \relax % manuscript
         \relax
       \or % acmsmall
         \relax
       \or % acmlarge
         \relax
       \or % acmtog
          \ACM@mk@linecount
       \or % sigconf
          \ACM@mk@linecount
       \or % siggraph
          \ACM@mk@linecount
       \or % sigplan
          \ACM@mk@linecount
       \or % sigchi
          \ACM@mk@linecount
       \or % sigchi-a
          \ACM@mk@linecount
       \or % acmengage
          \ACM@mk@linecount
       \or % acmcp
         \relax
    \fi
    \begin{picture}(0,0)%
      \put(20,-22){\usebox{\ACM@linecount@bx}}%
     \end{picture}%
  \fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\ACM@timestamp}
% \changes{v1.33}{2017/03/10}{Added macro (Michael D.~Adams)}
% \changes{v1.33}{2017/03/28}{Added current page number}
% \changes{v1.33}{2017/03/29}{Added submission id}
% \changes{v1.48}{2017/09/16}{Fromatting change (Michael D.~Adams)}
%  The timestamp system
%    \begin{macrocode}
\if@ACM@timestamp
  % Subtracting 30 from \time gives us the effect of rounding down despite
  % \numexpr rounding to nearest
  \newcounter{ACM@time@hours}
  \setcounter{ACM@time@hours}{\numexpr (\time - 30) / 60 \relax}
  \newcounter{ACM@time@minutes}
  \setcounter{ACM@time@minutes}{\numexpr \time - \theACM@time@hours * 60 \relax}
  \newcommand\ACM@timestamp{%
    \footnotesize%
    \ifx\@acmSubmissionID\@empty\relax\else
    Submission ID: \@acmSubmissionID.{ }%
    \fi
    \the\year-\two@digits{\the\month}-\two@digits{\the\day}{ }%
    \two@digits{\theACM@time@hours}:\two@digits{\theACM@time@minutes}{. }%
    Page \thepage\ of \@startPage--\pageref*{TotPages}.%
  }
\fi
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\@shortauthors}
% \changes{v1.15}{2016/07/04}{Introduced macro}
% \changes{v1.54}{2016/06/23}{Added submission id in anonymous mode}
%   Even if the author redefined \cs{shortauthors}, we do not print
%   it in the headers when in anonymous mode:
%    \begin{macrocode}
\def\@shortauthors{%
  \if@ACM@anonymous
    Anon.
    \ifx\@acmSubmissionID\@empty\else Submission Id: \@acmSubmissionID\fi
  \else\shortauthors\fi}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@headfootfont}
% \changes{v1.16}{2016/07/07}{Added macro}
% \changes{v1.48}{2017/09/16}{Deleted unnecessary switch (Michael D.~Adams)}
% \changes{v1.60}{2019/04/22}{Moved to footnotesize}
%   The font to typeset header and footer text.
%    \begin{macrocode}
\def\@headfootfont{\sffamily\footnotesize}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{standardpagestyle}
% \changes{v1.10}{2016/05/22}{Reversed folios location}
% \changes{v1.13}{2016/06/06}{Suppressed folios if sig}
% \changes{v1.13}{2016/06/06}{Added headers for sigs}
% \changes{v1.13}{2016/06/06}{Expanded headers for sigchi-a}
% \changes{v1.15}{2016/07/04}{Better handling of anonymous mode}
% \changes{v1.16}{2016/07/07}{Customize header/footer text font}
% \changes{v1.17}{2016/07/10}{Added paper title to sigs}
% \changes{v1.29}{2017/01/22}{Corrected printfolios (Matthew Fluet)}
% \changes{v1.33}{2017/03/10}{Added timestamp (Michael D.~Adams)}
% \changes{v1.33}{2017/03/29}{Added right linecount for two-column formats}
%   The page style for all pages but the first one
% \changes{v1.51}{2018/04/05}{We now use journal abbreviation for
% footers}
% \changes{v1.59}{2019/04/20}{Added logic for conference papers using
% journal format}
% \changes{v1.59}{2019/04/20}{Moved page styles to \cs{AtBeginDocument}}
% \changes{v1.76}{2021/02/21}{Fixed bug with no line numbers for
% nonacm}
% \changes{v1.86}{2022/06/26}{Special treatment of ACM Engage materials}
% \changes{v1.87}{2022/08/14}{Special treatment of ACMCP}
% \changes{v1.91}{2023/06/22}{Redesigned ACMP labels}
% \changes{v2.03}{2024/02/04}{Special treatment of ACM TOG}
%   The page style for all pages but the first one
%    \begin{macrocode}
\AtBeginDocument{%
\fancypagestyle{standardpagestyle}{%
  \fancyhf{}%
  \renewcommand{\headrulewidth}{\z@}%
  \renewcommand{\footrulewidth}{\z@}%
  \def\@acmArticlePage{%
    \ifx\@acmArticle\empty%
      \if@ACM@printfolios\thepage\fi%
    \else%
      \@acmArticle\if@ACM@printfolios:\thepage\fi%
    \fi%
  }%
  \if@ACM@journal@bibstrip@or@tog
    \ifcase\ACM@format@nr
    \relax % manuscript
      \fancyhead[LE]{\ACM@linecountL\if@ACM@printfolios\thepage\fi}%
      \fancyhead[RO]{\if@ACM@printfolios\thepage\fi}%
      \fancyhead[RE]{\@shortauthors}%
      \fancyhead[LO]{\ACM@linecountL\shorttitle}%
      \if@ACM@nonacm\else%
        \fancyfoot[RO,LE]{\footnotesize Manuscript submitted to ACM}
      \fi%
    \or % acmsmall
      \fancyhead[LE]{\ACM@linecountL\@headfootfont\@acmArticlePage}%
      \fancyhead[RO]{\@headfootfont\@acmArticlePage}%
      \fancyhead[RE]{\@headfootfont\@shortauthors}%
      \fancyhead[LO]{\ACM@linecountL\@headfootfont\shorttitle}%
      \if@ACM@nonacm\else%
        \fancyfoot[RO,LE]{\footnotesize \@journalNameShort, Vol. \@acmVolume, No.
        \@acmNumber, Article \@acmArticle.  Publication date: \@acmPubDate.}%
      \fi
    \or % acmlarge
      \fancyhead[LE]{\ACM@linecountL\@headfootfont
      \@acmArticlePage\quad\textbullet\quad\@shortauthors}%
      \fancyhead[LO]{\ACM@linecountL}%
      \fancyhead[RO]{\@headfootfont
        \shorttitle\quad\textbullet\quad\@acmArticlePage}%
      \if@ACM@nonacm\else%
        \fancyfoot[RO,LE]{\footnotesize \@journalNameShort, Vol. \@acmVolume, No.
        \@acmNumber, Article \@acmArticle.  Publication date: \@acmPubDate.}%
      \fi
    \or % acmtog
      \fancyhead[LE]{\ACM@linecountL\@headfootfont
        \@acmArticlePage\quad\textbullet\quad\@shortauthors}%
      \fancyhead[LO]{\ACM@linecountL}%
      \fancyhead[RE]{\ACM@linecountR}%
      \fancyhead[RO]{\@headfootfont
        \shorttitle\quad\textbullet\quad\@acmArticlePage\ACM@linecountR}%
      \if@ACM@nonacm\else
        \if@ACM@journal@bibstrip
          \fancyfoot[RO,LE]{\footnotesize \@journalNameShort,
            Vol. \@acmVolume, No.~\@acmNumber, Article \@acmArticle.
            Publication date: \@acmPubDate.}%
        \else
          \fancyfoot[RO,LE]{\footnotesize \acmConference@shortname,
            \acmConference@date, \acmConference@venue.}%
         \fi
      \fi
    \else % Proceedings
      \fancyfoot[C]{\if@ACM@printfolios\footnotesize\thepage\fi}%
      \fancyhead[LO]{\ACM@linecountL\@headfootfont\shorttitle}%
      \fancyhead[RE]{\@headfootfont\@shortauthors\ACM@linecountR}%
      \if@ACM@nonacm
        \fancyhead[LE]{\ACM@linecountL}%
        \fancyhead[RO]{\ACM@linecountR}%
      \else%
        \if@ACM@engage
          \fancyhead[LE]{\ACM@linecountL\@headfootfont\footnotesize
            EngageCSEdu. \ifx\@acmDOI\@empty\else\@formatdoi{\@acmDOI}\fi}%
          \fancyhead[RO]{\@headfootfont
            EngageCSEdu. \ifx\@acmDOI\@empty\else\@formatdoi{\@acmDOI}\fi
            \ACM@linecountR}%
        \else
          \fancyhead[LE]{\ACM@linecountL\@headfootfont\footnotesize
            \acmConference@shortname,
            \acmConference@date, \acmConference@venue}%
          \fancyhead[RO]{\@headfootfont
            \acmConference@shortname,
            \acmConference@date, \acmConference@venue\ACM@linecountR}%
         \fi
      \fi
    \fi
  \else % Proceedings
    \fancyfoot[C]{\if@ACM@printfolios\footnotesize\thepage\fi}%
    \fancyhead[LO]{\ACM@linecountL\@headfootfont\shorttitle}%
    \fancyhead[RE]{\@headfootfont\@shortauthors\ACM@linecountR}%
    \if@ACM@nonacm
      \fancyhead[LE]{\ACM@linecountL}%
      \fancyhead[RO]{\ACM@linecountR}%
    \else%
      \if@ACM@engage
        \fancyhead[LE]{\ACM@linecountL\@headfootfont
          EngageCSEdu. \ifx\@acmDOI\@empty\else\@formatdoi{\@acmDOI}\fi}%
        \fancyhead[RO]{\@headfootfont
          EngageCSEdu. \ifx\@acmDOI\@empty\else\@formatdoi{\@acmDOI}\fi
          \ACM@linecountR}%
      \else
        \fancyhead[LE]{\ACM@linecountL\@headfootfont
          \acmConference@shortname,
          \acmConference@date, \acmConference@venue}%
        \fancyhead[RO]{\@headfootfont
          \acmConference@shortname,
          \acmConference@date, \acmConference@venue\ACM@linecountR}%
       \fi
    \fi
  \fi
  \if@ACM@sigchiamode
     \fancyheadoffset[L]{\dimexpr(\marginparsep+\marginparwidth)}%
  \fi
  \if@ACM@timestamp
     \fancyfoot[LO,RE]{\ACM@timestamp}
  \fi
  \if@ACM@acmcp
  \renewcommand{\footrulewidth}{0.1\p@}%
  \fancyheadoffset[L]{46pt}%
  \fancyhead[L]{\makebox[\z@][l]{%
      \raisebox{-\dimexpr(0.2\textheight*(\ACM@ArticleType@nr-2))}{%
      \rotatebox{90}{\colorbox{@ACM@Article@color}{\color{white}%
          \strut\ACM@ArticleType~Article}}}}%
    \ACM@linecountL}%
  \fancyhead[R]{\makebox[\z@][r]{\box\@ACM@acmcpbox}}%
  \fancyfoot[L,C]{}%
  \fancyfoot[R]{\footnotesize
    \@journalName, Volume~\@acmVolume, Issue~\@acmNumber,
    \ifx\@acmArticle\@empty\else Article~\@acmArticle\fi\space
    (\@acmPubDate)\ifx\@acmDOI\@empty\else\\\@formatdoi{\@acmDOI}\fi}
  \fi
}%
\pagestyle{standardpagestyle}
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@folio@wd}
% \changes{v2.00}{2023/10/22}{Obsoleted macro}
% \begin{macro}{\@folio@ht}
% \changes{v2.00}{2023/10/22}{Obsoleted macro}
% \begin{macro}{\@folio@voffset}
% \changes{v2.00}{2023/10/22}{Obsoleted macro}
% \begin{macro}{\@folio@max}
% \changes{v2.00}{2023/10/22}{Obsoleted macro}
%   Folio blob width, height, offsets and max number
% \end{macro}
% \end{macro}
% \end{macro}
% \end{macro}
%
% \begin{macro}{\@folioblob}
% \changes{v1.44}{2017/08/11}{Suppress the blob if acmArticleSeq is empty}
% \changes{v1.45}{2017/08/15}{Switched \cs{bfeseries}\cs{sffamily} to
% \cs{sffamily}\cs{bfseries}}
% \changes{v2.00}{2023/10/22}{Obsoleted macro}
%   The macro to typeset the folio blob.
%
% \end{macro}
%
%
%
% \begin{macro}{firstpagestyle}
% \changes{v1.33}{2017/03/10}{Added timestamp (Michael D.~Adams)}
% \changes{v1.33}{2017/03/29}{Added right linecount for two-column
% formats}
% \changes{v1.51}{2018/04/05}{We now use journal abbreviation for
% footers}
% \changes{v1.59}{2019/04/20}{Added logic for conference papers using
% journal format}
% \changes{v1.59}{2019/04/20}{Moved page styles to \cs{AtBeginDocument}}
% \changes{v1.87}{2022/08/14}{Special treatment for acmcp}
% \changes{v1.89}{2022/12/27}{Moved badges here}
% \changes{v2.00}{2023/10/22}{Deleted blobs}
% \changes{v2.03}{2024/02/04}{Special treatment of tog}
%   The page style for the first page only.
%    \begin{macrocode}
\AtBeginDocument{%
\fancypagestyle{firstpagestyle}{%
  \fancyhf{}%
  \renewcommand{\headrulewidth}{\z@}%
  \renewcommand{\footrulewidth}{\z@}%
  \if@ACM@journal@bibstrip@or@tog
    \ifcase\ACM@format@nr
    \relax % manuscript
      \fancyhead[L]{\ACM@linecountL\@acmBadgeL}%
      \fancyhead[R]{\@acmBadgeR}%
      \fancyfoot[RO,LE]{\if@ACM@printfolios\small\thepage\fi}%
      \if@ACM@nonacm\else%
        \fancyfoot[RE,LO]{\footnotesize Manuscript submitted to ACM}%
      \fi%
    \or % acmsmall
      \if@ACM@nonacm\else%
        \fancyfoot[RO,LE]{\footnotesize \@journalNameShort, Vol. \@acmVolume, No.
        \@acmNumber, Article \@acmArticle.  Publication date:
        \@acmPubDate.}%
      \fi%
      \fancyhead[LE]{\ACM@linecountL\@acmBadgeL}%
      \fancyhead[LO]{\ACM@linecountL\@acmBadgeL}%
      \fancyhead[RO]{\@acmBadgeR}%
      \fancyhead[RE]{\@acmBadgeR}%
    \or % acmlarge
      \if@ACM@nonacm\else%
        \fancyfoot[RO,LE]{\footnotesize \@journalNameShort, Vol. \@acmVolume, No.
        \@acmNumber, Article \@acmArticle.  Publication date:
        \@acmPubDate.}%
      \fi%
      \fancyhead[RO]{\@acmBadgeR}%
      \fancyhead[RE]{\@acmBadgeR}%
      \fancyhead[LE]{\ACM@linecountL\@acmBadgeL}%
      \fancyhead[LO]{\ACM@linecountL\@acmBadgeL}%
    \or % acmtog
      \if@ACM@nonacm\else%
        \if@ACM@journal@bibstrip
          \fancyfoot[RO,LE]{\footnotesize \@journalNameShort,
            Vol. \@acmVolume, No.~\@acmNumber, Article \@acmArticle.
            Publication date: \@acmPubDate.}%
        \else
          \fancyfoot[RO,LE]{\footnotesize \acmConference@shortname,
            \acmConference@date, \acmConference@venue.}%
         \fi
      \fi%
      \fancyhead[L]{\ACM@linecountL\@acmBadgeL}%
      \fancyhead[R]{\@acmBadgeR\ACM@linecountR}%
    \else % Conference proceedings
      \fancyhead[L]{\ACM@linecountL\@acmBadgeL}%
      \fancyhead[R]{\@acmBadgeR\ACM@linecountR}%
      \fancyfoot[C]{\if@ACM@printfolios\footnotesize\thepage\fi}%
    \fi
  \else
    \fancyhead[L]{\ACM@linecountL\@acmBadgeL}%
    \fancyhead[R]{\@acmBadgeR\ACM@linecountR}%
    \fancyfoot[C]{\if@ACM@printfolios\footnotesize\thepage\fi}%
  \fi
  \if@ACM@timestamp
    \ifnum\ACM@format@nr=0\relax % Manuscript
    \fancyfoot[LO,RE]{\ACM@timestamp\quad
      \if@ACM@nonacm\else
        \footnotesize Manuscript submitted to ACM
      \fi}
    \else
    \fancyfoot[LO,RE]{\ACM@timestamp}
    \fi
  \fi
  \if@ACM@acmcp
  \renewcommand{\footrulewidth}{0.1\p@}%
  \fancyheadoffset[L]{46pt}%
  \fancyhead[L]{\makebox[\z@][l]{%
      \raisebox{-\dimexpr(0.2\textheight*(\ACM@ArticleType@nr-2))}{%
      \rotatebox{90}{\colorbox{@ACM@Article@color}{\color{white}%
          \strut\ACM@ArticleType~Article}}}}%
    \ACM@linecountL\@acmBadgeL}%
  \fancyhead[R]{\@acmBadgeR\makebox[\z@][r]{\box\@ACM@acmcpbox}}%
  \fancyfoot[L,C]{}%
  \fancyfoot[R]{\footnotesize
    \@journalName, Volume~\@acmVolume, Issue~\@acmNumber,
    \ifx\@acmArticle\@empty\else Article~\@acmArticle\fi\space
    (\@acmPubDate)\ifx\@acmDOI\@empty\else\\\@formatdoi{\@acmDOI}\fi}
  \fi
}}
%    \end{macrocode}
%
% \end{macro}
%
%
%\subsection{Sectioning}
%\label{sec:sectioninng}
%
%
% \begin{macro}{\ACM@NRadjust}
% \changes{v1.54}{2018/06/17}{Added macro}
% Ross Moore's macro.
% \begin{quotation}
%   Initially |\ACM@sect@format@| just passes through its |#1|
%   argument unchanged.
% When a sectioning macro such as |\section| occurs, the
% |\ACM@NRadjust| redefines |\ACM@sect@format@| to expand into the
% styling commands; e.g., |\@secfont|.
% Then |\Sectionformat| is temporarily redefined to produce
% |\realSectionformat{\ACM@sect@format{#1}}{#2}|, which ultimately
% becomes |\Sectionformat{\@secfont{#1}}{#2}|.
% This is done to allow |\Sectionformat| to be variable, as may be
% needed in particular circumstances. Its current expansion is saved
% as |\realSectionformat| which is used with the modified 1st argument,
% then |\Sectionformat| is reverted to its former expansion.
%
% To style the section-number, the line |#1{}| is used at the end of
% |\ACM@NRadjust|. This imposes style-changing commands for fonts and
% sizes, but any |\MakeUppercase| acts on just the |{}|. It's a bit hacky,
% by essentially assuming that only numbers occur here, not letters to
% be case-changed. (This should be acceptable in a class-file, as it
% ought to be possible to change the case elsewhere, if that was ever
% desired.) Also, it can result in |\@adddotafter| being called too
% soon, so a slight adjustment is made, via |\ACM@adddotafter|, which
% now tests whether its argument is empty. But the coding has to allow
% for other packages to have also patched |\@adddotafter|.
% \end{quotation}
%    \begin{macrocode}
\def\ACM@NRadjust#1{%
 \begingroup
  \expandafter\ifx\csname Sectionformat\endcsname\relax
  % do nothing when  \Sectionformat  is unknown
   \def\next{\endgroup #1}%
 \else
  \def\next{\endgroup
   \let\realSectionformat\Sectionformat
   \def\ACM@sect@format@{#1}%
   \let\Sectionformat\ACM@NR@adjustedSectionformat
 %%  next lines added 2018-06-17 to ensure section number is styled
   \let\real@adddotafter\@adddotafter
   \let\@adddotafter\ACM@adddotafter
   #1{}% imposes the styles, but nullifies  \MakeUppercase
   \let\@adddotafter\real@adddotafter
  }%
 \fi \next
}
%    \end{macrocode}
% \end{macro}
%
% \begin{macro}{\ACM@NR@adjustedSectionformat}
% \changes{v1.54}{2018/06/17}{Added macro}
%    \begin{macrocode}
\def\ACM@NR@adjustedSectionformat#1#2{%
 \realSectionformat{\ACM@sect@format{#1}}{#2}%
 \let\Sectionformat\realSectionformat}
\DeclareRobustCommand{\ACM@sect@format}{\ACM@sect@format@}
\def\ACM@sect@format@null#1{#1}
\let\ACM@sect@format@\ACM@sect@format@null
\AtBeginDocument{%
 \expandafter\ifx\csname LTX@adddotafter\endcsname\relax
  \let\LTX@adddotafter\@adddotafter
 \fi
}
%    \end{macrocode}
%
% \end{macro}
% \begin{macro}{\ACM@adddotafter}
% \changes{v1.54}{2018/06/17}{Added macro}
%    \begin{macrocode}
\def\ACM@adddotafter#1{\ifx\relax#1\relax\else\LTX@adddotafter{#1}\fi}
%    \end{macrocode}
%
% \end{macro}
%
% \changes{v1.54}{2018/05/27}{Deleted indent for subsubsection}
% \changes{v1.54}{2018/06/17}{Added Ross's patch for sectioning}
% \changes{v1.68}{2020/01/19}{New command for unindent paras}
% \changes{v1.69}{2020/02/02}{No dots for unindented paragraphs}
%   Sectioning is different for different levels
%    \begin{macrocode}
\renewcommand\section{\def\@toclevel{1}%
  \@startsection{section}{1}{\z@}%
  {-.75\baselineskip \@plus -2\p@ \@minus -.2\p@}%
  {.25\baselineskip}%
  {\ACM@NRadjust\@secfont}}
\renewcommand\subsection{\def\@toclevel{2}%
  \@startsection{subsection}{2}{\z@}%
  {-.75\baselineskip \@plus -2\p@ \@minus -.2\p@}%
  {.25\baselineskip}%
  {\ACM@NRadjust\@subsecfont}}
\renewcommand\subsubsection{\def\@toclevel{3}%
  \@startsection{subsubsection}{3}{\z@}%
  {-.5\baselineskip \@plus -2\p@ \@minus -.2\p@}%
  {-3.5\p@}%
  {\ACM@NRadjust{\@subsubsecfont\@adddotafter}}}
\renewcommand\paragraph{\def\@toclevel{4}%
  \@startsection{paragraph}{4}{\parindent}%
  {-.5\baselineskip \@plus -2\p@ \@minus -.2\p@}%
  {-3.5\p@}%
  {\ACM@NRadjust{\@parfont\@adddotafter}}}
\newcommand\noindentparagraph{\def\@toclevel{4}%
  \@startsection{paragraph}{4}{\z@}%
  {-.5\baselineskip \@plus -2\p@ \@minus -.2\p@}%
  {-3.5\p@}%
  {\ACM@NRadjust{\@parfont}}}
\renewcommand\part{\def\@toclevel{9}%
  \@startsection{part}{9}{\z@}%
  {-10\p@ \@plus -4\p@ \@minus -2\p@}%
  {4\p@}%
  {\ACM@NRadjust\@parfont}}
%    \end{macrocode}
%
% \begin{macro}{\section@raggedright}
%   \changes{v1.12}{2016/05/30}{Introduced macro}%
%   Special version of \cs{raggedright} compatible with
%   \cs{MakeUppercase}
%    \begin{macrocode}
\def\section@raggedright{\@rightskip\@flushglue
  \rightskip\@rightskip
  \leftskip\z@skip
  \parindent\z@}
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@secfont}
% \begin{macro}{\@subsecfont}
% \begin{macro}{\@subsubsecfont}
% \begin{macro}{\@parfont}
%  \changes{v1.12}{2016/05/30}{Moved to \cs{section@raggedright}}%
% \changes{v1.40}{2017/06/04}{Switched to MakeTextUppercase}
% \changes{v1.45}{2017/08/15}{Switched \cs{bfeseries}\cs{sffamily} to
% \cs{sffamily}\cs{bfseries}}
% \changes{v1.67}{2020/01/11}{Deleted redefinitions of sections}
% \changes{v1.85}{2022/05/08}{Added: acmengage}
% \changes{v2.08}{2024/05/27}{Deleted uppercasing}
% Fonts for sections etc. are different for different formats.
%    \begin{macrocode}
\def\@secfont{\sffamily\bfseries\section@raggedright}
\def\@subsecfont{\sffamily\bfseries\section@raggedright}
\def\@subsubsecfont{\sffamily\itshape}
\def\@parfont{\itshape}
\setcounter{secnumdepth}{3}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
\or % acmlarge
 \def\@secfont{\sffamily\large\section@raggedright}
 \def\@subsecfont{\sffamily\large\section@raggedright}
\or % acmtog
 \def\@secfont{\sffamily\large\section@raggedright}
 \def\@subsecfont{\sffamily\large\section@raggedright}
\or % sigconf
 \def\@secfont{\bfseries\Large\section@raggedright}
 \def\@subsecfont{\bfseries\Large\section@raggedright}
\or % siggraph
 \def\@secfont{\sffamily\bfseries\Large\section@raggedright}
 \def\@subsecfont{\sffamily\bfseries\Large\section@raggedright}
\or % sigplan
 \def\@secfont{\bfseries\Large\section@raggedright}
 \def\@subsecfont{\bfseries\section@raggedright}
 \def\@subsubsecfont{\bfseries\section@raggedright}
 \def\@parfont{\bfseries\itshape}
 \def\@subparfont{\itshape}
\or % sigchi
 \setcounter{secnumdepth}{1}
 \def\@secfont{\sffamily\bfseries\section@raggedright}
 \def\@subsecfont{\sffamily\bfseries\section@raggedright}
\or % sigchi-a
 \setcounter{secnumdepth}{0}
 \def\@secfont{\sffamily\bfseries\section@raggedright}
 \def\@subsecfont{\sffamily\bfseries\section@raggedright}
\or % acmengage
 \def\@secfont{\bfseries\Large\section@raggedright}
 \def\@subsecfont{\bfseries\Large\section@raggedright}
\or %acmcp
\fi
%    \end{macrocode}
%
% \end{macro}
% \end{macro}
% \end{macro}
% \end{macro}
%
% \changes{v2.10}{2024/10/19}{Added tocindent for paragraphs and
% subparagraphs} 
% Amasat does not define tocindents for paragraphs and subparagraphs.
% Here we add them
%    \begin{macrocode}
\AtBeginDocument{%
  \@for\@tempa:=-1,0,1,2,3,4,5\do{%
    \@ifundefined{r@tocindent\@tempa}{%
      \@xp\gdef\csname r@tocindent\@tempa\endcsname{0pt}}{}%
  }%
}
\def\@writetocindents{%
  \begingroup
  \@for\@tempa:=-1,0,1,2,3,4,5\do{%
    \immediate\write\@auxout{%
      \string\newlabel{tocindent\@tempa}{%
        \csname r@tocindent\@tempa\endcsname}}%
  }%
  \endgroup}
%    \end{macrocode}
%
% \begin{macro}{\@adddotafter}
%   Add punctuation after a sectioning command
%    \begin{macrocode}
\def\@adddotafter#1{#1\@addpunct{.}}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@addspaceafter}
%   Add space after a sectioning command
%    \begin{macrocode}
\def\@addspaceafter#1{#1\@addpunct{\enspace}}
%    \end{macrocode}
%
% \end{macro}
%
% \changes{v1.87}{2022/08/14}{No numbering sections for ACMCP}
%    \begin{macrocode}
\if@ACM@acmcp
   \setcounter{secnumdepth}{-1}%
\fi
%    \end{macrocode}
% 
%
%\subsection{TOC lists}
%\label{sec:tocs}
%
% \begin{macro}{\@dotsep}
% Related to the \cs{tableofcontents} are all the horizontal fillers. Base
% \LaTeX\ defines \cs{@dottedtocline}, which we should not disable. Yet, this
% command expects \cs{@dotsep} to be defined but leaves this to the class
% implementation. Since |amsart| does not provide this, we copy the standard
% variant from |article| here.
%    \begin{macrocode}
\providecommand*\@dotsep{4.5}
%    \end{macrocode}
% \end{macro}
%
%
%
%\subsection{Theorems}
%\label{sec:theorems}
%
% \begin{macro}{\@acmplainbodyfont}
%   The font to typeset the body of the |acmplain| theorem style.
%    \begin{macrocode}
\def\@acmplainbodyfont{\itshape}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@acmplainindent}
%   The amount to indent the |acmplain| theorem style.
%    \begin{macrocode}
\def\@acmplainindent{\parindent}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@acmplainheadfont}
%   The font to typeset the head of the |acmplain| theorem style.
%    \begin{macrocode}
\def\@acmplainheadfont{\scshape}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@acmplainnotefont}
%   The font to typeset the note of the |acmplain| theorem style.
%    \begin{macrocode}
\def\@acmplainnotefont{\@empty}
%    \end{macrocode}
%
% \end{macro}
%
% Customization of the |acmplain| theorem style:
%    \begin{macrocode}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
\or % acmlarge
\or % acmtog
\or % sigconf
\or % siggraph
\or % sigplan
  \def\@acmplainbodyfont{\itshape}
  \def\@acmplainindent{\z@}
  \def\@acmplainheadfont{\bfseries}
  \def\@acmplainnotefont{\normalfont}
\or % sigchi
\or % sigchi-a
\or % acmengage
\or % acmcp
\fi
%    \end{macrocode}
%
% \begin{macro}{acmplain}
%   The |acmplain| theorem style
%    \begin{macrocode}
\newtheoremstyle{acmplain}%
  {.5\baselineskip\@plus.2\baselineskip
    \@minus.2\baselineskip}% space above
  {.5\baselineskip\@plus.2\baselineskip
    \@minus.2\baselineskip}% space below
  {\@acmplainbodyfont}% body font
  {\@acmplainindent}% indent amount
  {\@acmplainheadfont}% head font
  {.}% punctuation after head
  {.5em}% spacing after head
  {\thmname{#1}\thmnumber{ #2}\thmnote{ {\@acmplainnotefont(#3)}}}% head spec
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\@acmdefinitionbodyfont}
%   The font to typeset the body of the |acmdefinition| theorem style.
%    \begin{macrocode}
\def\@acmdefinitionbodyfont{\normalfont}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@acmdefinitionindent}
%   The amount to indent the |acmdefinition| theorem style.
%    \begin{macrocode}
\def\@acmdefinitionindent{\parindent}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@acmdefinitionheadfont}
%   The font to typeset the head of the |acmdefinition| theorem style.
%    \begin{macrocode}
\def\@acmdefinitionheadfont{\itshape}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@acmdefinitionnotefont}
%   The font to typeset the note of the |acmdefinition| theorem style.
%    \begin{macrocode}
\def\@acmdefinitionnotefont{\@empty}
%    \end{macrocode}
%
% \end{macro}
%
% Customization of the |acmdefinition| theorem style:
%    \begin{macrocode}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
\or % acmlarge
\or % acmtog
\or % sigconf
\or % siggraph
\or % sigplan
  \def\@acmdefinitionbodyfont{\normalfont}
  \def\@acmdefinitionindent{\z@}
  \def\@acmdefinitionheadfont{\bfseries}
  \def\@acmdefinitionnotefont{\normalfont}
\or % sigchi
\or % sigchi-a
\or % acmengage
\or % acmcp
\fi
%    \end{macrocode}
%
% \begin{macro}{acmdefinition}
%   The |acmdefinition| theorem style
%    \begin{macrocode}
\newtheoremstyle{acmdefinition}%
  {.5\baselineskip\@plus.2\baselineskip
    \@minus.2\baselineskip}% space above
  {.5\baselineskip\@plus.2\baselineskip
    \@minus.2\baselineskip}% space below
  {\@acmdefinitionbodyfont}% body font
  {\@acmdefinitionindent}% indent amount
  {\@acmdefinitionheadfont}% head font
  {.}% punctuation after head
  {.5em}% spacing after head
  {\thmname{#1}\thmnumber{ #2}\thmnote{ {\@acmdefinitionnotefont(#3)}}}% head spec
%    \end{macrocode}
%
% \end{macro}
%
% Make |acmplain| the default theorem style.
%    \begin{macrocode}
\theoremstyle{acmplain}
%    \end{macrocode}
%
% Delay defining the theorem environments until after other packages
% have been loaded.  In particular, the |cleveref| package must be
% loaded before the theorem environments are defined in order to show
% the correct environment name (see
% \url{https://github.com/borisveytsman/acmart/issues/138}).  The |acmthm|
% option is used to suppress the definition of any theorem
% environments.  Also, to avoid obscure errors arising from these
% environment definitions conflicting with environments defined by the
% user or by user-loaded packages, we only define environments that
% have not yet been defined.
%    \begin{macrocode}
\AtEndPreamble{%
  \if@ACM@acmthm
  \theoremstyle{acmplain}
  \@ifundefined{theorem}{%
  \newtheorem{theorem}{Theorem}[section]
  }{}
  \@ifundefined{conjecture}{%
  \newtheorem{conjecture}[theorem]{Conjecture}
  }{}
  \@ifundefined{proposition}{%
  \newtheorem{proposition}[theorem]{Proposition}
  }{}
  \@ifundefined{lemma}{%
  \newtheorem{lemma}[theorem]{Lemma}
  }{}
  \@ifundefined{corollary}{%
  \newtheorem{corollary}[theorem]{Corollary}
  }{}
  \theoremstyle{acmdefinition}
  \@ifundefined{example}{%
  \newtheorem{example}[theorem]{Example}
  }{}
  \@ifundefined{definition}{%
  \newtheorem{definition}[theorem]{Definition}
  }{}
  \fi
  \theoremstyle{acmplain}
}
%    \end{macrocode}
%
%
% \begin{macro}{\@proofnamefont}
%   The font to typeset the proof name.
%    \begin{macrocode}
\def\@proofnamefont{\scshape}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\@proofindent}
%   Whether or not to indent proofs.
%    \begin{macrocode}
\def\@proofindent{\indent}
%    \end{macrocode}
%
% \end{macro}
%
% Customization of the proof environment.
%    \begin{macrocode}
\ifcase\ACM@format@nr
\relax % manuscript
\or % acmsmall
\or % acmlarge
\or % acmtog
\or % sigconf
\or % siggraph
\or % sigplan
  \def\@proofnamefont{\itshape}
  \def\@proofindent{\noindent}
\or % sigchi
\or % sigchi-a
\or % acmengage
\or % acmcp
\fi
%    \end{macrocode}
%
% \begin{macro}{proof}
%   We want some customization of the proof environment.
%    \begin{macrocode}
\renewenvironment{proof}[1][\proofname]{\par
  \pushQED{\qed}%
  \normalfont \topsep6\p@\@plus6\p@\relax
  \trivlist
  \item[\@proofindent\hskip\labelsep
        {\@proofnamefont #1\@addpunct{.}}]\ignorespaces
}{%
  \popQED\endtrivlist\@endpefalse
}
%    \end{macrocode}
%
% \end{macro}
%
%
%
%\subsection{Balancing columns}
%\label{sec:balancing}
%
% We need balancing only if the user did not disable it, and we use a
% two column format.  Note that |pbalance| uses a different
% mechanism.
%    \begin{macrocode}
\AtEndPreamble{%
  \if@ACM@pbalance
    \global\@ACM@balancefalse
    \ifcase\ACM@format@nr
    \relax % manuscript
       \or % acmsmall
       \or % acmlarge
       \or % acmtog
          \RequirePackage{pbalance}%
       \or % sigconf
          \RequirePackage{pbalance}%
       \or % siggraph
          \RequirePackage{pbalance}%
       \or % sigplan
          \RequirePackage{pbalance}%
       \or % sigchi
          \RequirePackage{pbalance}%
       \or % sigchi-a
       \or % acmengage
          \RequirePackage{pbalance}%
       \or % acmcp
    \fi
  \fi
  \if@ACM@balance
    \ifcase\ACM@format@nr
    \relax % manuscript
         \global\@ACM@balancefalse
       \or % acmsmall
         \global\@ACM@balancefalse
       \or % acmlarge
         \global\@ACM@balancefalse
       \or % acmtog
          \RequirePackage{balance}%
       \or % sigconf
          \RequirePackage{balance}%
       \or % siggraph
          \RequirePackage{balance}%
       \or % sigplan
          \RequirePackage{balance}%
       \or % sigchi
          \RequirePackage{balance}%
       \or % sigchi-a
          \global\@ACM@balancefalse
       \or % acmengage
          \RequirePackage{balance}%
       \or % acmcp
         \global\@ACM@balancefalse
    \fi
  \fi
}
\AtEndDocument{%
  \if@ACM@balance
  \if@twocolumn
  \balance
  \fi\fi}
%    \end{macrocode}
%
%
%\subsection{Acknowledgments}
%\label{sec:acks}
%
% \begin{macro}{\acksname}
% \changes{v1.56}{2018/10/16}{Added macro (Philip Quinn)}
%    \begin{macrocode}
\newcommand\acksname{Acknowledgments}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\acks}
% \changes{v1.19}{2016/07/28}{Include 'Acknowledgements' in PDF bookmarks
% (Matthew Fluet)}
% \changes{v1.15}{2016/07/05}{Added macro}
%   This is a |comment|-like structure
%    \begin{macrocode}
\specialcomment{acks}{%
  \begingroup
  \section*{\acksname}
  \phantomsection\addcontentsline{toc}{section}{\acksname}
}{%
  \endgroup
}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\grantsponsor}
% \changes{v1.15}{2016/07/05}{Added macro}
% \changes{v1.17}{2016/07/10}{Renamed}
% We just typeset the name of the sponsor
%    \begin{macrocode}
\def\grantsponsor#1#2#3{#2}
%    \end{macrocode}
%
% \end{macro}
%
% \begin{macro}{\grantnum}
% \changes{v1.15}{2016/07/05}{Added macro}
% \changes{v1.17}{2016/07/10}{Added url}
%    \begin{macrocode}
\newcommand\grantnum[3][]{#3%
  \def\@tempa{#1}\ifx\@tempa\@empty\else\space(\url{#1})\fi}
%    \end{macrocode}
%
% \end{macro}
%
% \subsection{Conditional typesetting}
% \label{sec:conditional}
%
% We use the |comment| package for conditional typesetting:
%    \begin{macrocode}
\AtEndPreamble{%
\if@ACM@screen
  \includecomment{screenonly}
  \excludecomment{printonly}
\else
  \excludecomment{screenonly}
  \includecomment{printonly}
\fi
\if@ACM@anonymous
  \excludecomment{anonsuppress}
  \excludecomment{acks}
\else
  \includecomment{anonsuppress}
\fi}
%    \end{macrocode}
%
%
%
%\subsection{Additional bibliography commands}
%\label{sec:bibliography}
%
% \begin{macro}{\showeprint}
% \changes{v1.21}{2016/09/04}{Added macro}
% The command \cs{showeprint} has two arguments: the (optional)
% prefix and the eprint number.  Right now the only prefix we understand is
% the (lowercase) word `arxiv'.
%    \begin{macrocode}
\newcommand\showeprint[2][arxiv]{%
  \def\@tempa{#1}%
  \ifx\@tempa\@empty\def\@tempa{arxiv}\fi
  \def\@tempb{arxiv}%
  \ifx\@tempa\@tempb\relax
     arXiv:\href{https://arxiv.org/abs/#2}{#2}%
  \else
     \def\@tempb{arXiv}%
     \ifx\@tempa\@tempb\relax
       arXiv:\href{https://arxiv.org/abs/#2}{#2}%
     \else
       #1:#2%
    \fi
  \fi}
%    \end{macrocode}
%
%
% \end{macro}
%
%
%\subsection{Index}
%\label{sec:index}
%
% \begin{macro}{\theindex}
% Standard |amsart| uses plain page style in index pages.  This
% clashes with line numbering for |review| option, so we redefine it.
%    \begin{macrocode}
\def\theindex{\@restonecoltrue\if@twocolumn\@restonecolfalse\fi
  \columnseprule\z@ \columnsep 35\p@
  \@indextitlestyle
  \let\item\@idxitem
  \parindent\z@  \parskip\z@\@plus.3\p@\relax
  \raggedright
  \hyphenpenalty\@M
  \footnotesize}
%    \end{macrocode}
%
% \end{macro}
%
%\subsection{End of Class}
%\label{end}
%
% \begin{macro}{\vspace}
% \changes{v1.57}{2018/12/16}{Added warning}
% \changes{v1.58}{2019/01/23}{Preserved old versions as
% \cs{@vspace@orig} and \cs{@vspace@orig}}
% \changes{1.69}{2020/02/02}{Added the changed command to avoid grouping}
% We do not disable \cs{vspace}, but add warning to it
%    \begin{macrocode}
\let\@vspace@orig=\@vspace
\let\@vspacer@orig=\@vspacer
\apptocmd{\@vspace}{\ClassWarning{\@classname}{\string\vspace\space should
    only be used to provide space above/below surrounding
    objects}}{}{}
\apptocmd{\@vspacer}{\ClassWarning{\@classname}{\string\vspace\space should
    only be used to provide space above/below surrounding
    objects}}{}{}
\let\@vspace@acm=\@vspace
\let\@vspacer@acm=\@vspacer
%    \end{macrocode}
%
% \end{macro}
%
%
% \begin{macro}{\ACM@origbaselinestretch}
% \changes{v1.57}{2018/12/16}{Added check whether the user changed
% \cs{baselinestretch}}
% We produce an error if the user tries to change \cs{baselinestretch}
%    \begin{macrocode}
\let\ACM@origbaselinestretch\baselinestretch
\AtEndDocument{\ifx\baselinestretch\ACM@origbaselinestretch\else
  \ClassError{\@classname}{An attempt to redefine
    \string\baselinestretch\space detected.  Please do not do this for
    ACM submissions!}\fi}
%    \end{macrocode}
%
% \end{macro}
% \begin{macro}{\ACM@origsection}
% \changes{v2.14}{2025/05/30}{Added check whether the user changed
% \cs{section}}
% We produce an error if the user tries to change \cs{section}
%    \begin{macrocode}
\let\ACM@origsection\section
\AtEndDocument{\ifx\section\ACM@origsection\else
  \ClassError{\@classname}{An attempt to redefine
    \string\section\space detected.  Please do not do this for
    ACM submissions!}\fi}
%    \end{macrocode}
%
% \end{macro}
% \begin{macro}{\ACM@origsubsection}
% \changes{v2.14}{2025/05/30}{Added check whether the user changed
% \cs{subsection}}
% We produce an error if the user tries to change \cs{subsection}
%    \begin{macrocode}
\let\ACM@origsubsection\subsection
\AtEndDocument{\ifx\subsection\ACM@origsubsection\else
  \ClassError{\@classname}{An attempt to redefine
    \string\subsection\space detected.  Please do not do this for
    ACM submissions!}\fi}
%    \end{macrocode}
%
% \end{macro}
% \begin{macro}{\ACM@origsubsubsection}
% \changes{v2.14}{2025/05/30}{Added check whether the user changed
% \cs{subsubsection}}
% We produce an error if the user tries to change \cs{subsubsection}
%    \begin{macrocode}
\let\ACM@origsubsubsection\subsubsection
\AtEndDocument{\ifx\subsubsection\ACM@origsubsubsection\else
  \ClassError{\@classname}{An attempt to redefine
    \string\subsubsection\space detected.  Please do not do this for
    ACM submissions!}\fi}
%    \end{macrocode}
%
% \end{macro}
% \begin{macro}{\ACM@origparagraph}
% \changes{v2.14}{2025/05/30}{Added check whether the user changed
% \cs{paragraph}}
% We produce an error if the user tries to change \cs{paragraph}
%    \begin{macrocode}
\let\ACM@origparagraph\paragraph
\AtEndDocument{\ifx\paragraph\ACM@origparagraph\else
  \ClassError{\@classname}{An attempt to redefine
    \string\paragraph\space detected.  Please do not do this for
    ACM submissions!}\fi}
%    \end{macrocode}
%
% \end{macro}
%
% \changes{v1.30}{2017/02/10}{Added \cs{frenchspacing}}
%    \begin{macrocode}
\normalsize\normalfont\frenchspacing
%</class>
%    \end{macrocode}
%
%\Finale
%\clearpage
%
%\PrintChanges
%\clearpage
%\PrintIndex
%
\endinput
