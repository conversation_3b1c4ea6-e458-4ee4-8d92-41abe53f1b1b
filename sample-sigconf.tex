%%
%% This is file `sample-sigconf.tex',
%% generated with the docstrip utility.
%%
%% The original source files were:
%%
%% samples.dtx  (with options: `all,proceedings,bibtex,sigconf')
%% 
%% IMPORTANT NOTICE:
%% 
%% For the copyright see the source file.
%% 
%% Any modified versions of this file must be renamed
%% with new filenames distinct from sample-sigconf.tex.
%% 
%% For distribution of the original source see the terms
%% for copying and modification in the file samples.dtx.
%% 
%% This generated file may be distributed as long as the
%% original source files, as listed above, are part of the
%% same distribution. (The sources need not necessarily be
%% in the same archive or directory.)
%%
%%
%% Commands for TeXCount
%TC:macro \cite [option:text,text]
%TC:macro \citep [option:text,text]
%TC:macro \citet [option:text,text]
%TC:envir table 0 1
%TC:envir table* 0 1
%TC:envir tabular [ignore] word
%TC:envir displaymath 0 word
%TC:envir math 0 word
%TC:envir comment 0 0
%%
%% The first command in your LaTeX source must be the \documentclass
%% command.
%%
%% For submission and review of your manuscript please change the
%% command to \documentclass[manuscript, screen, review]{acmart}.
%%
%% When submitting camera ready or to TAPS, please change the command
%% to \documentclass[sigconf]{acmart} or whichever template is required
%% for your publication.
%%
%%
\documentclass[sigconf]{acmart}
%%
%% \BibTeX command to typeset BibTeX logo in the docs
%\AtBeginDocument{%
%  \providecommand\BibTeX{{%
%    Bib\TeX}}}

\copyrightyear{2025}
\acmYear{2025}
\setcopyright{cc}
\setcctype{by}
\acmConference[MM '25]{Proceedings of the 33rd ACM International Conference on Multimedia}{October 27--31, 2025}{Dublin, Ireland}
\acmBooktitle{Proceedings of the 33rd ACM International Conference on Multimedia (MM '25), October 27--31, 2025, Dublin, Ireland}\acmDOI{10.1145/3746027.3755703}
\acmISBN{979-8-4007-2035-2/2025/10}

% 1 Authors, replace the red X's with your assigned DOI string during the rightsreview eform process.
% 2 Your DOI link will become active when the proceedings appears in the DL.
% 3 Retain the DOI string between the curly braces for uploading your presentation video.

\settopmatter{printacmref=true, authorsperrow=4}



%%
%% Submission ID.
%% Use this when submitting an article to a sponsored event. You'll
%% receive a unique submission ID from the organizers
%% of the event, and this ID should be used as the parameter to this command.
%%\acmSubmissionID{123-A56-BU3}

%%
%% For managing citations, it is recommended to use bibliography
%% files in BibTeX format.
%%
%% You can then either use BibTeX with the ACM-Reference-Format style,
%% or BibLaTeX with the acmnumeric or acmauthoryear sytles, that include
%% support for advanced citation of software artefact from the
%% biblatex-software package, also separately available on CTAN.
%%
%% Look at the sample-*-biblatex.tex files for templates showcasing
%% the biblatex styles.
%%

%%
%% The majority of ACM publications use numbered citations and
%% references.  The command \citestyle{authoryear} switches to the
%% "author year" style.
%%
%% If you are preparing content for an event
%% sponsored by ACM SIGGRAPH, you must use the "author year" style of
%% citations and references.
%% Uncommenting
%% the next command will enable that style.
%%\citestyle{acmauthoryear}


\usepackage{graphicx}       
\usepackage{array}           
\usepackage{booktabs} 
\usepackage{colortbl} 
\usepackage{multirow}        
\usepackage{hhline}          
\usepackage{caption}        
\usepackage{tabularx} 
\usepackage{booktabs}
\usepackage{makecell}
\usepackage{enumitem}
\usepackage{tcolorbox}
\usepackage{booktabs}
\usepackage{adjustbox}
\usepackage{fancyhdr}
\usepackage{fontawesome}

%%
%% Optimize author area to reduce vertical space
%% while maintaining readability and completeness
%%
\makeatletter
% Reduce author font size from \LARGE to \large
\renewcommand{\@authorfont}{\large}
% Reduce affiliation font size from \large to \small
\renewcommand{\@affiliationfont}{\small}
% Reduce spacing between author boxes from 1pc to 0.5pc
\author@bx@sep=0.5pc\relax

% Further optimize the author box typesetting to reduce vertical space
% \renewcommand{\@typeset@author@bx}{\bgroup\hsize=\author@bx@wd
%   \def\and{\par}\normalbaselines
%   % Use tighter line spacing within author boxes
%   \lineskip=2pt\relax
%   \global\setbox\author@bx=\vtop{\if@ACM@sigchiamode\else\centering\fi
%     \@authorfont\@currentauthors\par\vskip 2pt\@affiliationfont
%     \@currentaffiliation}\egroup
%   \box\author@bx\hspace{\author@bx@sep}%
%   \gdef\@currentauthors{}%
%   \gdef\@currentaffiliation{}}

%%
%% Define commands to reduce vertical spacing at specific locations
%% This approach is safer than redefining section commands
%%
\newcommand{\reducesectionspacing}{\vspace{-0.3\baselineskip}}
\newcommand{\reducesubsectionspacing}{\vspace{-0.2\baselineskip}}
\makeatother

%%
%% end of the preamble, start of the body of the document source.
\begin{document}

%%
%% The "title" command has an optional parameter,
%% allowing the author to define a "short title" to be used in page headers.
\title[Embodied-R: Collaborative Framework for Activating Embodied Spatial Reasoning in Foundation Models via Reinforcement Learning]{Embodied-R: Collaborative Framework for Activating\\ Embodied Spatial Reasoning in Foundation Models via \\ Reinforcement Learning}

%%
%% The "author" command and its associated commands are used to define
%% the authors and their affiliations.
%% Of note is the shared affiliation of the first two authors, and the
%% "authornote" and "authornotemark" commands
%% used to denote shared contribution to the research.



\author{Baining Zhao}
\authornote{Both authors contributed equally to this research.}
\affiliation{%
	\institution{Shenzhen International Graduate School, Tsinghua University \\ Pengcheng Laboratory}
	\city{Shenzhen}
	\country{China}}
    
\author{Ziyou Wang}
\authornotemark[1]
\affiliation{%
	\institution{Northeastern University}
	\city{Qinhuangdao}
	\country{China}}
	
\author{Jianjie Fang}
\authornotemark[1]
\affiliation{%
	\institution{Northeastern University}
	\city{Qinhuangdao}
	\country{China}}

\author{Chen Gao}
\authornote{Corresponding authors: Chen Gao, Xinlei Chen}
\affiliation{%
	\institution{BNRist, Tsinghua University}
	\city{Beijing}
	\country{China}}
\email{<EMAIL>}

\author{Fanhang Man}
\affiliation{%
	\institution{Shenzhen International Graduate School, Tsinghua University}
	\city{Shenzhen}
	\country{China}}
	
\author{Jinqiang Cui}
\affiliation{%
	\institution{Pengcheng Laboratory}
	\city{Shenzhen}
	\country{China}}

\author{Xin Wang}
\affiliation{%
	\institution{Department of Computer Science and Technology, Tsinghua University \\ BNRist, Tsinghua University}
	\city{Beijing}
	\country{China}}
	
\author{Xinlei Chen}
\authornotemark[2]
\affiliation{%
	\institution{Shenzhen International Graduate School, Tsinghua University}
	\city{Shenzhen}
	\country{China}}
\email{<EMAIL>}
	
\author{Yong Li}
\affiliation{%
	\institution{Department of Electronic Engineering, Tsinghua University \\ BNRist, Tsinghua University}
	\city{Beijing}
	\country{China}}
	
\author{Wenwu Zhu}
\affiliation{%
	\institution{Department of Computer Science and Technology, Tsinghua University \\ BNRist, Tsinghua University}
	\city{Beijing}
	\country{China}}

%%
%% By default, the full list of authors will be used in the page
%% headers. Often, this list is too long, and will overlap
%% other information printed in the page headers. This command allows
%% the author to define a more concise list
%% of authors' names for this purpose.
\renewcommand{\shortauthors}{Zhao et al.}
\renewcommand{\shorttitle}{Embodied-R: Collaborative Framework for Activating Embodied Spatial Reasoning \\ in Foundation Models via Reinforcement Learning}


%%
%% The abstract is a short summary of the work to be presented in the
%% article.
\input{0_abstract}

%%
%% The code below is generated by the tool at http://dl.acm.org/ccs.cfm.
%% Please copy and paste the code instead of the example below.
%%
\begin{CCSXML}
	<ccs2012>
	<concept>
	<concept_id>10010147.10010178</concept_id>
	<concept_desc>Computing methodologies~Artificial intelligence</concept_desc>
	<concept_significance>500</concept_significance>
	</concept>
	</ccs2012>
\end{CCSXML}

\ccsdesc[500]{Computing methodologies~Artificial intelligence}
%%
%% Keywords. The author(s) should pick words that accurately describe
%% the work being presented. Separate the keywords with commas.
\keywords{Vision-Language Model; Embodied Intelligence; Reasoning}
%% A "teaser" image appears between the author and affiliation
%% information and the body of the document, and typically spans the
%% page.
\begin{teaserfigure}
	\centering
	\includegraphics[width=0.95 \textwidth]{fig/task.png}
	\vspace{-3pt}
	\caption{Embodied spatial reasoning: tasks and thinking process. Challenging tasks from public embodied video datasets are identified, encompassing both indoor and outdoor scenarios. We introduce slow-thinking to improve reasoning performance.}
	\label{fig:task}
\end{teaserfigure}

\received{20 February 2007}
\received[revised]{12 March 2009}
\received[accepted]{5 June 2009}

%%
%% This command processes the author and affiliation and title
%% information and builds the first part of the formatted document.
\maketitle


\input{1_intro}
\input{4_related_work}
\input{2_method}
\input{3_experiments}
\input{6_further_exploaration}
\input{5_conclusion}

\section*{Acknowledgments}
This paper was supported by the National Key R\&D program of China (2022YFC3300703), the Natural Science Foundation of China under Grant 62371269, Guangdong Innovative and Entrepreneurial Research Team Program (2021ZT09L197), Meituan Academy of Robotics Shenzhen, Talent Program of Guangdong Province (2021QN0\\2Z107), and National Science and Technology Major Project (2024ZD\\01NL00103).


\bibliographystyle{ACM-Reference-Format}
\balance
\bibliography{sample-base}

\clearpage
\input{appendix}

\end{document}
\endinput
%%
%% End of file `sample-sigconf.tex'.
