%
% Doctrip file for acmart
% This file is in public domain
% $Id: acmart.ins,v 1.1 2015/11/23 22:42:55 boris Exp $
%
\def\batchfile{acmart.ins}
\input docstrip
\keepsilent
\showprogress


\askforoverwritefalse

\generate{%
  \file{acmart.cls}{\from{acmart.dtx}{class}}
  \file{acmart-tagged.cls}{\from{acmart.dtx}{class,tagged}}
}

\obeyspaces
\Msg{*****************************************************}%
\Msg{* Congratulations!  You successfully  generated the *}%
\Msg{* acmart package.                                   *}%
\Msg{*                                                   *}%
\Msg{* Please move the file acmart.cls to where LaTeX    *}%
\Msg{* files are stored in  your system.  The manual is  *}%
\Msg{* acmart.pdf.                                       *}%
\Msg{*                                                   *}%
\Msg{* The package is released under LPPL                *}%
\Msg{*                                                   *}%
\Msg{* Happy TeXing!                                     *}%
\Msg{*****************************************************}%