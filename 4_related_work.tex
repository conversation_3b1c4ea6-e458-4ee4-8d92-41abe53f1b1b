\section{Related Work}

\noindent \textbf{Large Language Model Reasoning.} 
Recently, enhancing reasoning capabilities has become a key focus in large model technologies, demonstrating remarkable performance on tasks such as mathematical and logical problem-solving~\cite{imani2023mathprompter,yang2024qwen2,qwq-32b-preview}. Following the release of OpenAI's o1~\cite{openai2024b}, numerous studies have proposed various technical approaches to achieve similar functionalities, including Chain-of-Thought (CoT)~\cite{wei2022chain}, Monte Carlo Tree Search (MCTS)~\cite{guan2025rstar,zhang2025rest}, distillation~\cite{min2024imitate}, rejection sampling combined with supervised fine-tuning (SFT) or Direct Preference Optimization (DPO)~\cite{setlur2025rl}, among others. Furthermore, Deepseek-r1~\cite{guo2025deepseek} introduced a method to foster the emergence of reasoning abilities in large language models (LLMs) through rule-based rewards combined with reinforcement learning. Similarly, Kimi k1.5~\cite{team2025kimi} proposed a comparable approach, presenting various training techniques, such as curriculum learning. This reinforcement learning paradigm has sparked significant interest, with subsequent works successfully reproducing related results~\cite{xie2025logic,zeng2025simplerl}.

\noindent \textbf{Embodied Spatial Reasoning with VLMs.}
Inspired by the generality of foundation models across various domains~\cite{achiam2023gpt,ahn2024autort}, embodied intelligence aims to develop agents that utilize large multimodal models as their "brains" to achieve perception, navigation, and manipulation in the 3D physical world~\cite{driess2023palm,shah2023lm}. In terms of input, human visual-spatial perception is more akin to continuous RGB observations, similar to video streams~\cite{cheng2024videgothink,suglia2024alanavlm}, rather than static images~\cite{thawakar2025llamav} or point clouds~\cite{wang2024embodiedscan}. Several embodied video benchmarks~\cite{yang2024thinking} demonstrate that, while perception tasks are relatively well-addressed, spatial reasoning tasks—such as spatial relationship inference, navigation, and planning—remain highly challenging. However, existing research~\cite{fei2024video,sun2025video} on video reasoning primarily focuses on disembodied content reasoning, with little emphasis on scenarios involving embodied continuous visual inputs.

\noindent \textbf{Collaboration between large and small models.}
Existing research primarily focuses on addressing the resource consumption and privacy risks associated with large models, as well as the efficiency and performance advantages of small models in specific scenarios~\cite{wang2024comprehensive}. Small models can assist large models in data selection, prompt optimization, and reasoning enhancement~\cite{zhang2023effective,li2024purifying}. The use of small models to detect hallucinations and privacy leakage is explored in~\cite{ulmer2024calibrating,zhao2023automatic}, improving overall system reliability. While our work shares the goal of reducing computational resource demands, it differs by emphasizing the complementary roles of large-scale VLMs in perception and small-scale LMs in enhancing embodied spatial reasoning.