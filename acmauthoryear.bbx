\ProvidesFile{acmauthoryear.bbx}[2022-02-14 v0.1 biblatex bibliography style]

% Inherit a default style
\RequireBibliographyStyle{authoryear-comp}

%%% New command definitions from trad-standard.bbx

\newcommand*{\newcommaunit}{\@ifstar\newcommaunitStar\newcommaunitNoStar}
\newcommand*{\newcommaunitStar}{\setunit*{\addcomma\space}}
\newcommand*{\newcommaunitNoStar}{\setunit{\addcomma\space}}

%%% Forward compatibility for date+extradate

\ifcsundef{ifbibmacroundef}{
  \ifcsundef{abx@macro@date+extradate}{ %%% For really really old biblatex that miss \ifbibmacroundef
    \blx@warning{bibmacro 'date+extradate' is missing.\MessageBreak
      Please consider updating your version of biblatex.\MessageBreak
      Using 'date+extrayear' instead}%
    \providebibmacro*{date+extradate}{\usebibmacro{date+extrayear}}
    }{}
  }
  {
  \ifbibmacroundef{date+extradate}{
    \blx@warning{bibmacro 'date+extradate' is missing.\MessageBreak
      Please consider updating your version of biblatex.\MessageBreak
      Using 'date+extrayear' instead}%
    \providebibmacro*{date+extradate}{\usebibmacro{date+extrayear}}
    }{}
  }

%%% Localisation strings for ACM

\DefineBibliographyStrings{american}{%
  mathesis = {Master's thesis},
  phdthesis = {Ph\adddot{}D\adddotspace Dissertation},
  editor = {(Ed\adddot)},
  editors = {(Eds\adddot)},
  edition = {ed\adddot},
}



%%% Formatting for fields

%\DeclareFieldFormat
%  [article,inbook,incollection,inproceedings,patent,thesis,unpublished]
%  {title}{#1}
\DeclareFieldFormat{pages}{#1}

\DeclareFieldFormat{numpages}{#1 pages}

\DeclareFieldFormat{number}{#1}

\DeclareFieldFormat{articleno}{Article #1}

\DeclareFieldFormat{key}{#1}

\DeclareFieldFormat{urldate}{Retrieved\space{}#1\space{}from}
\DeclareFieldFormat{lastaccessed}{Retrieved\space{}#1\space{}from}

\DeclareFieldFormat{url}{\url{#1}}

\DeclareFieldFormat{edition}{%
  \printtext[parens]{\ifinteger{#1}
    {\mkbibordedition{#1}~\bibstring{edition}}
    {#1\isdot~\bibstring{edition}}}}


% Handle urls field containing 'and' separated list of URLs
% https://github.com/plk/biblatex/issues/229
\DeclareListFormat{urls}{%
  \url{#1}%
  \ifthenelse{\value{listcount}<\value{liststop}}
    {\addcomma\space}
    {}}
\renewbibmacro*{url}{\iffieldundef{url}{\printlist{urls}}{\printfield{url}}}

%%% Bibmacro definitions

\renewbibmacro*{translator+others}{%
  \ifboolexpr{
    test \ifusetranslator
    and
    not test {\ifnameundef{translator}}
  }
    {\printnames{translator}%
     \setunit{\addcomma\space}%
     \usebibmacro{translator+othersstrg}%
     \clearname{translator}}
    {\printfield{key}}}

\newbibmacro*{year}{%
  \iffieldundef{year}%
    {\printtext{[n.\ d.]}}%
    {\printfield{year}}%
}

\renewbibmacro*{date}{\printtext[parens]{\printdate}}


\renewbibmacro*{url+urldate}{\iffieldundef{urlyear}
    {\iffieldundef{lastaccessed}
     {}
     {\printfield{lastaccessed}%
     \setunit*{\addspace}}%
    }
    {\usebibmacro{urldate}%
     \setunit*{\addspace}}%
     \usebibmacro{url}%
  }

\renewbibmacro*{journal+issuetitle}{%
  \usebibmacro{journal}%
  \setunit*{\addcomma\space}%
  \iffieldundef{series}
    {}
    {\newunit%
     \printfield{series}%
     \setunit{\addspace}}%
  \usebibmacro{volume+number+date+pages+eid}%
  \newcommaunit%
%  \setunit{\addspace}%
  \usebibmacro{issue-issue}%
  \setunit*{\addcolon\space}%
  \usebibmacro{issue}%
  \newunit}



\newbibmacro*{volume+number+date+pages+eid}{%
  \printfield{volume}%
  \setunit*{\addcomma\space}%
  \printfield{number}%
  \setunit*{\addcomma\space}%
  \printfield{articleno}
  \setunit{\addcomma\space}
  \usebibmacro{date-ifmonth}
  \setunit{\addcomma\space}%
  \iffieldundef{pages}%
    {\printfield{numpages}}%
    {\printfield{pages}}%
  \newcommaunit%
  \printfield{eid}}%

\renewbibmacro*{chapter+pages}{%
  \printfield{chapter}%
  \setunit{\bibpagespunct}%
  \iffieldundef{pages}%
    {\printfield{numpages}}%
    {\printfield{pages}}%
  \newunit}

\renewbibmacro*{editor+others}{%
  \ifboolexpr{
    test \ifuseeditor
    and
    not test {\ifnameundef{editor}}
  }
    {\printnames{editor}%
     \setunit{\addcomma\space}%
     \usebibmacro{editor+othersstrg}%
     \clearname{editor}}
     {\iflistundef{organization}{}{\printlist{organization}}}
     \usebibmacro{date+extradate}
     }


\newbibmacro*{issue-issue}{%
  \iffieldundef{issue}%
    {}%
    {\printfield{issue}%
     \setunit*{\addcomma\space}%
     \usebibmacro{date-ifmonth}%
    }%
  \newunit}




\newbibmacro*{maintitle+booktitle+series+number}{%
  \iffieldundef{maintitle}
    {}
    {\usebibmacro{maintitle}%
     \newunit\newblock
     \iffieldundef{volume}
       {}
       {\printfield{volume}%
        \printfield{part}%
        \setunit{\addcolon\space}}}%
  \usebibmacro{booktitle}%
  \setunit*{\addspace}
  \printfield[parens]{series}%
  \setunit*{\addspace}%
  \printfield{number}%
  \setunit*{\addcomma\space}%
  \printfield{articleno}
  \newunit
  }

\renewbibmacro*{booktitle}{%
  \ifboolexpr{
    test {\iffieldundef{booktitle}}
    and
    test {\iffieldundef{booksubtitle}}
  }
    {}
    {\printtext[booktitle]{%
       \printfield[titlecase]{booktitle}%
       \iffieldundef{booksubtitle}{}{
       \setunit{\subtitlepunct}%
       \printfield[titlecase]{booksubtitle}}%
       }%
    }%
  \printfield{booktitleaddon}}

\renewbibmacro*{volume+number+eid}{%
  \printfield{volume}%
  \setunit*{\addcomma\space}%
  \printfield{number}%
  \setunit*{\addcomma\space}%
  \printfield{articleno}
  \setunit{\addcomma\space}%
  \printfield{eid}}


\renewbibmacro*{publisher+location+date}{%
  \printlist{publisher}%
  \setunit*{\addcomma\space}%
  \printlist{location}%
  \setunit*{\addcomma\space}%
  \usebibmacro{date-ifmonth}%
  \newunit}


\newbibmacro{date-ifmonth}{%
  \iffieldundef{month}{}{%
    \usebibmacro{date}
  }%
}


\renewbibmacro*{institution+location+date}{%
  \printlist{school}%
  \setunit*{\addcomma\space}%
  \printlist{institution}%
  \setunit*{\addcomma\space}%
  \printlist{location}%
  \setunit*{\addcomma\space}%
  \usebibmacro{date-ifmonth}%
  \newunit}


\renewbibmacro*{periodical}{%
  \iffieldundef{title}
    {}
    {\printtext[title]{%
       \printfield[titlecase]{title}%
       \setunit{\subtitlepunct}%
       \printfield[titlecase]{subtitle}}}%
       \newunit%
       \usebibmacro{journal}}

\renewbibmacro*{issue+date}{%
    \iffieldundef{issue}
      {\usebibmacro{date}}
      {\printfield{issue}%
       \setunit*{\addspace}%
       \usebibmacro{date}}%
  \newunit}

\renewbibmacro*{title+issuetitle}{%
  \usebibmacro{periodical}%
  \setunit*{\addspace}%
  \iffieldundef{series}
    {}
    {\newunit
     \printfield{series}%
     \setunit{\addspace}}%
  \printfield{volume}%
  \setunit*{\addcomma\space}%
  \printfield{number}%
  \setunit*{\addcomma\space}%
  \printfield{articleno}
  \setunit{\addcomma\space}%
  \printfield{eid}%
  \setunit{\addspace}%
  \usebibmacro{issue+date}%
  \setunit{\addcolon\space}%
  \usebibmacro{issue}%
  \newunit}

\renewbibmacro*{doi+eprint+url}{%
  \iftoggle{bbx:url}
    {\iffieldundef{doi}{
      \usebibmacro{url+urldate}
      }{\iffieldundef{distinctURL}
      {}
      {\usebibmacro{url+urldate}}
      }
    }%
  \newunit\newblock
  \iftoggle{bbx:eprint}
    {\usebibmacro{eprint}}
    {}%
  \newunit\newblock
  \iftoggle{bbx:doi}
    {\printfield{doi}}
    {}}


%%% Definitions for drivers (alphabetical)



\DeclareBibliographyDriver{article}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author/translator+others}%
  \setunit{\labelnamepunct}\newblock%
  \usebibmacro{title}%
  \newunit%
  \printlist{language}%
  \newunit\newblock%
  \usebibmacro{byauthor}%
  \newunit\newblock%
  \usebibmacro{bytranslator+others}%
  \newunit\newblock%
  \printfield{version}%
  \newunit\newblock%
  \usebibmacro{journal+issuetitle}%
  \newunit%
  \usebibmacro{byeditor+others}%
  \newunit%
  \printfield{note}%
  \newunit\newblock%
  \iftoggle{bbx:isbn}
    {\printfield{isbn}}
    {}%
  \newunit\newblock%
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock%
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock%
  \usebibmacro{related}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{book}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author/editor+others/translator+others}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{maintitle+title}%
  \newunit%
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \usebibmacro{byeditor+others}%
  \newunit\newblock
  \printfield{edition}%
  \newunit
  \usebibmacro{series+number}%
  \iffieldundef{maintitle}
    {\printfield{volume}%
     \printfield{part}}
    {}%
  \newunit
  \newunit\newblock
  \printfield{volumes}%
  \newunit\newblock
  \printfield{note}%
  \newunit\newblock
  \usebibmacro{publisher+location+date}%
  \newunit\newblock
  \usebibmacro{chapter+pages}%
  \newunit
  \printfield{pagetotal}%
  \newunit\newblock
  \iftoggle{bbx:isbn}
    {\printfield{isbn}}
    {}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{inbook}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \iffieldundef{author}%
    {\usebibmacro{byeditor+others}}%
    {\usebibmacro{author/translator+others}}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
%  \usebibmacro{in:}%
  \usebibmacro{bybookauthor}%
  \newunit\newblock
  \usebibmacro{maintitle+booktitle}%
  \newunit\newblock
  \iffieldundef{author}{}%if undef then we already printed editor
    {\usebibmacro{byeditor+others}}%
  \newunit\newblock
  \printfield{edition}%
  \newunit
  \iffieldundef{maintitle}
    {\printfield{volume}%
     \printfield{part}}
    {}%
  \newunit
  \printfield{volumes}%
  \newunit\newblock
  \usebibmacro{series+number}%
  \newunit\newblock
  \printfield{note}%
  \newunit\newblock
  \usebibmacro{publisher+location+date}%
  \newunit\newblock
  \usebibmacro{chapter+pages}%
  \newunit\newblock
  \iftoggle{bbx:isbn}
    {\printfield{isbn}}
    {}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{incollection}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author/translator+others}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \usebibmacro{in:}%
  \usebibmacro{maintitle+booktitle}%
  \newunit\newblock
  \usebibmacro{series+number}%
  \newunit\newblock
  \printfield{edition}%
  \newunit
  \iffieldundef{maintitle}
    {\printfield{volume}%
     \printfield{part}}
    {}%
  \newunit
  \printfield{volumes}%
  \newunit\newblock
  \usebibmacro{byeditor+others}%
  \newunit\newblock
  \printfield{note}%
  \newunit\newblock
  \usebibmacro{publisher+location+date}%
  \newunit\newblock
  \usebibmacro{chapter+pages}%
  \newunit\newblock
  \iftoggle{bbx:isbn}
    {\printfield{isbn}}
    {}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{inproceedings}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author/translator+others}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \usebibmacro{in:}%
  \usebibmacro{maintitle+booktitle+series+number}%
  \newunit\newblock
  \usebibmacro{event+venue+date}%
  \newunit\newblock
  \usebibmacro{byeditor+others}%
  \newunit\newblock
  \iffieldundef{maintitle}
    {\printfield{volume}%
     \printfield{part}}
    {}%
  \newunit
  \printfield{volumes}%
  \newunit\newblock
  \printfield{note}%
  \newunit\newblock
  \printlist{organization}%
  \newunit
  \usebibmacro{publisher+location+date}%
  \newunit\newblock
  \usebibmacro{chapter+pages}%
  \newunit\newblock
  \iftoggle{bbx:isbn}
    {\printfield{isbn}}
    {}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{manual}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author/editor+others}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \usebibmacro{byeditor}%
  \newunit\newblock
  \printfield{edition}%
  \newunit\newblock
  \usebibmacro{series+number}%
  \newunit\newblock
  \printfield{type}%
  \newunit
  \printfield{version}%
  \newunit
  \printfield{note}%
  \newunit\newblock
  \printlist{organization}%
  \newunit
  \usebibmacro{publisher+location+date}%
  \newunit\newblock
  \usebibmacro{chapter+pages}%
  \newunit
  \printfield{pagetotal}%
  \newunit\newblock
  \iftoggle{bbx:isbn}
    {\printfield{isbn}}
    {}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{misc}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author/editor+others/translator+others}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \usebibmacro{byeditor+others}%
  \newunit\newblock
  \printfield{howpublished}%
  \newunit\newblock
  \printfield{type}%
  \newunit
  \printfield{version}%
  \newunit
  \printfield{note}%
  \newunit\newblock
  \usebibmacro{organization+location+date}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{online}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author/editor+others/translator+others}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \usebibmacro{byeditor+others}%
  \newunit\newblock
  \printfield{version}%
  \newunit
  \printfield{note}%
  \newunit\newblock
  \printlist{organization}%
  \newunit\newblock
  \usebibmacro{date-ifmonth}%
  \newunit\newblock
  \iftoggle{bbx:eprint}
    {\usebibmacro{eprint}}
    {}%
  \newunit\newblock
  \usebibmacro{url+urldate}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareFieldFormat[patent]{number}{Patent No.~#1}

\DeclareBibliographyDriver{patent}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \usebibmacro{date}%
  \newunit\newblock
  \printfield{type}%
  \setunit*{\addspace}%
  \printfield{number}%
  \iflistundef{location}
    {}
    {\setunit*{\addspace}%
     \printtext[parens]{%
       \printlist[][-\value{listtotal}]{location}}}%
  \newunit\newblock
  \usebibmacro{byholder}%
  \newunit\newblock
  \printfield{note}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{periodical}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{editor}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title+issuetitle}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byeditor}%
  \newunit\newblock
  \printfield{note}%
  \newunit\newblock
  \iftoggle{bbx:isbn}
    {\printfield{issn}}
    {}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{report}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \printfield{type}%
  \setunit*{\addspace}%
  \printfield{number}%
  \newunit\newblock
  \printfield{version}%
  \newunit
  \printfield{note}%
  \newunit\newblock
  \usebibmacro{institution+location+date}%
  \newunit\newblock
  \usebibmacro{chapter+pages}%
  \newunit
  \printfield{pagetotal}%
  \newunit\newblock
  \iftoggle{bbx:isbn}
    {\printfield{isrn}}
    {}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}



\DeclareBibliographyDriver{thesis}{%
  \usebibmacro{bibindex}%
  \usebibmacro{begentry}%
  \usebibmacro{author}%
  \setunit{\labelnamepunct}\newblock
  \usebibmacro{title}%
  \newunit
  \printlist{language}%
  \newunit\newblock
  \usebibmacro{byauthor}%
  \newunit\newblock
  \printfield{type}%
  \newunit
  \usebibmacro{institution+location+date}%
  \newunit\newblock
  \usebibmacro{chapter+pages}%
  \newunit
  \printfield{pagetotal}%
  \newunit\newblock
  \iftoggle{bbx:isbn}
    {\printfield{isbn}}
    {}%
  \newunit\newblock
  \usebibmacro{doi+eprint+url}%
  \newunit\newblock
  \usebibmacro{addendum+pubstate}%
  \setunit{\bibpagerefpunct}\newblock
  \usebibmacro{pageref}%
  \newunit\newblock
  \printfield{note}%
  \newunit\newblock
  \iftoggle{bbx:related}
    {\usebibmacro{related:init}%
     \usebibmacro{related}}
    {}%
  \usebibmacro{finentry}}

%
% Include support for software entries
%
\blx@inputonce{software.bbx}{biblatex style for software}{}{}{}{}

%
% Handle ACM specific ArtifactSoftware entry exactly as the software entry (a soft alias will not work)
%
\DeclareStyleSourcemap{
  \maps[datatype=bibtex]{
   \map{
     \step[typesource=artifactsoftware,typetarget=software]
     \step[typesource=artifactdataset,typetarget=dataset]
   }
  }
}

%%% Compatibility with ACM bibtex formatting


%
% Show given name first in the reference list
%
\DeclareNameAlias{sortname}{given-family}

%
% Produce a bibliography with small font size
%
\renewcommand*{\bibfont}{\bibliofont\footnotesize}

%
% Remove parentheses from date+extradate
%
\RequirePackage{xpatch}
\xpatchbibmacro{date+extradate}{%
  \printtext[parens]%
}{%
  \newblock\setunit*{.\space}%
  \printtext%
}{}{}


%%% Set option values for ACM style

\ExecuteBibliographyOptions{
  dashed=false,     % Do not use dashes for bibliography items with the same set of authors
  labeldate=year,
  abbreviate=true,
  dateabbrev=true,
  isbn=true,
  doi=true,
  urldate=comp,
  url=true,
  maxbibnames=9,
  maxcitenames=2,
  backref=false,
  sorting=nty,
  halid=true,
  swhid=true,
  swlabels=true,
  vcs=true,
  license=false,
  language=american
  }

% We use lowercase DOI

\DeclareFieldFormat{doi}{%
  doi\addcolon
  \ifhyperref
    {\href{https://doi.org/#1}{\nolinkurl{#1}}}
    {\nolinkurl{#1}}}
