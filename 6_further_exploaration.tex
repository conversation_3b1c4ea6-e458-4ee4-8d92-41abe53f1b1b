\vspace{-0.3cm}
\section{Further Exploration}


Building upon the aforementioned experiments, we further explore four intriguing RQs related to embodied video-based RL training:
\begin{itemize}[leftmargin=*]
	\item \textbf{RQ4: What Is the Relationship Between Inference Ability, Aha Moments, and Response Length?}

	\item \textbf{RQ5: Why Not Directly Perform RL Training on VLLMs?}

	\item \textbf{RQ6: Is Accuracy+Format Rewards All You Need?}

    	\item \textbf{RQ7: RL vs SFT when Generalize to Out-of-Distribution (OOD) Embodied Tasks?}
\end{itemize}

\subsection{Relationship Between Inference Ability, Aha Moments, and Response Length?}
% 将训练图画出，折线图

The GRPO training process is illustrated in Figure~\ref{Fig:further_exploration}a-d, which correspond to the validation set's accuracy reward, format reward, ratio of logical consistency reward to accuracy reward, and the response length, respectively. Notably, existing pure-text-based reproductions~\cite{xie2025logic,zeng2025simplerl} of DeepSeek-R-Zero models identify inference ability and the "aha moment" as key indicators of emergent reasoning capabilities. However, such phenomena are rarely observed in other multimodal reasoning tasks, such as image-based reasoning~\cite{chen2025r1v,liu2025visual}. This leads us to hypothesize that response length is strongly influenced by the nature of the question itself. For instance, mathematical problems often require multi-step calculations, where increased reasoning length tends to correlate positively with reasoning ability. In contrast, for multimodal reasoning tasks like embodied spatial reasoning, the LM model training process converges toward an optimal range of text output distributions. Concise reasoning patterns may facilitate the embodied spatial reasoning. This highlights the versatility of RL-based post-training method, demonstrating the ability to benefit a wide range of reasoning tasks.

\reducesubsectionspacing
\subsection{Why Not Directly Perform RL on VLLMs?}
We previously attempted direct RL training on the Qwen-VL-3B-Instruct model. As shown in Figure~\ref{Fig:further_exploration}e, under similar training parameters and time, the performance of the VLM was notably inferior to that of the LM. Upon convergence, the VLM achieved an accuracy of 43.8\% on the test set, significantly lower than the LM. The limited perceptual capability of the VLM restricts its potential for reasoning improvements. Therefore, under resource-constrained conditions, collaborative inference integrating models of different scales present a promising solution.
\reducesubsectionspacing

\subsection{Is Accuracy+Format Rewards All You Need?}
According to the Deepseek-R1-Zero, it appears that accuracy and format rewards are enough to guide the model toward correct reasoning. However, during training in our problem, we observed instances of reward hacking, where the model optimizes the answer but the reasoning process leading to that answer is inconsistent with the answer itself. We aim to ensure alignment between the model's reasoning process and its answer, both to enhance generalization and improve the interpretability of the reasoning process. As shown in Figure~\ref{Fig:further_exploration}f, we employ GPT-4o to evaluate the proportion of logically consistent outputs on the test set before and after incorporating a logical consistency reward. This proportion increased from 46.01\% to 99.43\% after the reward was added, demonstrating the value of this approach in addressing embodied spatial multiple-choice reasoning tasks. Moreover, this reward mechanism could potentially be extended to other reasoning tasks prone to answer accuracy hacking during training.

% \vspace{-0.4cm}
\reducesubsectionspacing
\subsection{RL vs SFT when Generalize to Out-of-Distribution (OOD) Embodied Tasks?}

For small-scale LMs, we aim to explore their generalization performance when trained with SFT instead of RL. To evaluate this, we introduced two OOD datasets: EgoSchema and the egocentric task in MVBench. As discussed in Sections~\ref{sec:data_prepare}, these two OOD datasets differ significantly from the training set in both task content and scene characteristics.
The accuracy results are shown in Figure~\ref{Fig:further_exploration}g. RL-trained models demonstrate generalization ability across both datasets. On the EgoSchema dataset, the RL-trained language model under the Embodied-R framework even achieve performance comparable to the state-of-the-art multimodal reasoning model, Gemini-2.5-Pro. SFT-trained models showed improvement on EgoSchema but a decline on MVBench. This suggests that slow reasoning, as employed in RL models, could be a promising approach to improve the generalization capabilities even for small-scale models.




