

\section{The Embodied-R Method}
\label{sec:method}

We first define the problem of embodied spatial reasoning. Subsequently, we introduce the VLM-based perception module and the LM-based reasoning module. The collaborative framework is shown in Figure \ref{Fig:framework}.

\begin{figure*}[h]
	\centering
	\includegraphics[width = \linewidth]{fig/framework.png}
	% \vspace{-20pt}
	\caption{The proposed Embodied-R is a collaborative embodied spatial reasoning framework integrating a Vision-Language Model (VLM) and a Language Model (LM). The separation of perception and reasoning enables us to leverage the perceptual capabilities of large-scale VLMs while training a resource-efficient small-scale LM to activate embodied reasoning through RL. Notably, we introduce a novel logical consistency reward to guide the LM in producing logically coherent reasoning and answer.}
	\label{Fig:framework}
	% \vspace{-5pt}
\end{figure*}


\subsection{Problem Formulation}
In the physical world, an agent moves through space, generating a sequence of video frames (continuous visual observations) $ {\bf{f}} = [f_0, f_1, \dots, f_T]$. Suppose a spatial reasoning problem is denoted as $ q $. Our goal is to build a model that takes $q$ and $\bf{f}$ as inputs and outputs an answer $a$. The answer $a$ is considered correct if it is semantically consistent with the ground truth $g$; otherwise, it is deemed incorrect.

\subsection{Large-Scale VLM-based Perception}

\subsubsection{\textbf{Key-Frame Extractor}}

As the agent moves continuously in space, high sampling frequencies result in significant overlap between consecutive frames. On one hand, the VLM relies on changes in the static objects within the environment across frames to infer the agent's pose variation. On the other hand, excessive overlap between frames leads to increased inference costs for both the VLM and LLM. To address this, we designed a key-frame extractor tailored to the characteristics of embodied videos, selecting key frames that retain overlap while ensuring sufficient information gain between them.

The extraction of key-frames is based on the overlap of visual fields caused by motion continuity. When the agent moves forward, the visual content in the latter frame is expected to overlap with a portion of the former frame, and the reverse is true when moving backward. Similarly, during left or right rotations, the latter frame should partially overlap with the former frame in the horizontal direction, and during upward or downward rotations, the overlap occurs in the vertical direction. Given that the sampling frequency of visual observations is typically much higher than the agent's motion speed, frames generally exhibit significant overlap.

Specifically, a perspective transformation is used to model the geometric relationship between frames. Assuming \( f_t \) is a key-frame, to determine whether \( f_{t+1} \) should also be considered a keyframe, keypoints and descriptors are calculated from \( f_t \) and \( f_{t+1} \) using the Oriented FAST and Rotated BRIEF (ORB) algorithm.
Next, a feature matching algorithm, such as the Brute-Force Matcher, is applied to match the descriptors between the two frames and the Random Sample Consensus (RANSAC) algorithm is employed to estimate the homography matrix. The overlap ratio between two frames is then computed. If overlap ratio is less than a predefined threshold, it indicates significant visual changes between the frames, and \( f_{t+1} \) is marked as a key-frame. Otherwise, the algorithm proceeds to calculate the overlap ratio between \( f_t \) and \( f_{t+2} \). This process continues until a new key-frame is identified, which then becomes the reference for subsequent frames. 
Considering the effect of viewpoint changes, rotations (both horizontal and vertical) result in larger field-of-view variations, leading to more frames being recorded during these movements. If the indices of the extracted keyframes are denoted as \( {\bf{f}}' = \left[ f_{k_0}, f_{k_1}, \dots, f_{k_n} \right] \), the keyframe extraction process can be summarized as:
\begin{equation}
{\bf{f}}' = \text{K-Extractor}(\bf{f}).
\end{equation}

\subsubsection{\textbf{Embodied Semantic Representation}}
Since perceptual capability is positively correlated with model size~\cite{li2024mvbench,zhao2025urbanvideobench,yang2024thinking}, we employ a large-scale VLM to process visual inputs to ensure high-quality perception.
The differential information of each key frame is described sequentially. This approach provides two key benefits: 1) The sequential and dynamic processing aligns better with the characteristics of embodied scenarios, where visual observations are continuously generated over time. At each moment, the model should integrate historical semantic representations with the latest visual observations, rapidly updating the semantic understanding of spatial perception. 2) It facilitates the handling of long videos by avoiding the input token limitations that arise when all frames are processed simultaneously by the VLM. 

Specifically, for the first frame, the VLM identifies the objects present in the scene, their attributes, and their spatial locations. For subsequent frames, both the previous frame and the current frame are input into the VLM to extract key semantic representation ${s_{{k_j}}}$:
\begin{equation}
	{s_{{k_j}}} \sim \psi_\theta (s|{f_{{k_{j - 1}}}},{f_{{k_j}}};q),\:j = 1,2,...,n,
\end{equation}
where ${s_{{k_j}}}$ consists of three items:
\begin{itemize}[leftmargin=*]
	\item \textbf{Action}: Inferring the agent's actions based on the changes in visual observations between consecutive frames.
	\item \textbf{$\triangle \text{Information}$}: Determining changes in the spatial relationships between the agent and known objects, as well as identifying whether new objects appear in the field of view.
	\item \textbf{$q$-related} content: Detecting whether objects or information relevant to the reasoning task appear in the latest field of view.  
\end{itemize}
In this way, we can extract spatial semantic representations \( {\mathbf{s}} = [{s_{{k_0}}},{s_{{k_1}}},...,{s_{{k_n}}}] \) from the keyframe \({\bf{f}}'\).

\subsection{Small-Scale LM-based Reasoning}
Given semantic perception, we can train a training-friendly small-scale language model capable of performing embodied spatial reasoning.
Assuming the small-scale LM is denoted as \( \pi_\theta \), the response \( o \) inferred from the model can be expressed as: $o \sim \pi_\theta(o \mid q, \mathbf{s}).$
	
Our training objective is to ensure that the model adheres to the "think-then-answer" paradigm, where the thinking process is logical, and the answer is correct.
We follow DeepSeek-R1-Zero and adopt a computationally efficient RL training strategy, Group Relative Policy Optimization (GRPO). Besides rule-based format and accuracy rewards, we propose a novel reasoning process reward tailored for embodied reasoning tasks to mitigate reward hacking and enhance the logical consistency between the reasoning process and the final answer. 

\subsubsection{\textbf{Group Relative Policy Optimization}}

For a given query \( q \) and semantic annotation $\mathbf{s}$, GRPO generates a group of outputs \(\{o_1, o_2, \ldots, o_G\}\) using the reference policy \( \pi_{\text{ref}} \). The reference policy typically refers to the original model not trained via GRPO. The policy model \( \pi_\theta \) is then updated by optimizing the following objective:

\begin{equation}
\small
\begin{aligned}
	\mathcal{J}(\theta) & =  \mathbb{E}_{\left( {q,{\bf{s}}} \right) \sim \mathbb{D}, \{o_i\}_{i=1}^G \sim \pi_{\text{old}}(o|{q,{\bf{s}}})} \Bigg[\frac{1}{G}\sum\limits_{i = 1}^G \Bigg( \min \Bigg(\frac{{{\pi _\theta }({o_i}|q,{\bf{s}})}}{{{\pi _{{\rm{old}}}}({o_i}|q,{\bf{s}})}}{A_i}, \\
	&\text{clip} \Bigg( 
	\frac{\pi_\theta(o_i|q,{\bf{s}})}{\pi_{\text{old}}(o_i|q,{\bf{s}})}, 
	1 - \epsilon, 
	1 + \epsilon 
	\Bigg) A_i 
	\Bigg) - \beta \mathcal{D}_{\text{KL}}(\pi_\theta \| \pi_{\text{ref}}) 
	\Bigg) 
	\Bigg], 
\end{aligned}
\end{equation}
where \( \epsilon \) and \( \beta \) are hyperparameters, and \( \mathcal{D}_{\text{KL}}(\pi_\theta \| \pi_{\text{ref}}) \) is KL divergence penalty: $	\mathcal{D}_{\text{KL}}(\pi_\theta \| \pi_{\text{ref}}) = \pi_{\text{ref}}(r_i|q,{\bf{s}}) \log \frac{\pi_{\text{ref}}(r_i|q,{\bf{s}})}{\pi_\theta(r_i|q,{\bf{s}})} - 1$. \( A_i \) represents the advantage corresponding to the output \( o_i \), calculated from the corresponding \( \{r_1, r_2, \ldots, r_G\} \): $	A_i = \frac{r_i - \text{mean}(\{r_1, r_2, \ldots, r_G\})}{\text{std}(\{r_1, r_2, \ldots, r_G\})}$.

\subsubsection{\textbf{Reward Modeling}}
Reward modeling is a critical component of RL algorithms, as their design guides the direction of model optimization. We propose three types of rewards: format reward, accuracy reward, and logical consistency reward. These are designed to respectively guide the model to learn the "think-answer" reasoning pattern, accurate embodied spatial reasoning, and logical consistency between reasoning and the answer.

\noindent \textbf{Format Reward:} 
We aim for the model to output $o_i$ by first producing an embodied reasoning process $p_i$ followed by the final answer $a_i$. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively:
\begin{tcolorbox}[boxrule=0mm]
	\textit{Please assume the role of an agent. Given a question and a series of frames, you should first think about the reasoning process in the mind and then provide the final answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> <answer> answer here </answer>. Ensure that your answer is consistent with and directly derived from your thinking process, maintaining logical coherence between the two sections. The frames represent your egocentric observations from the past to the present. Question: $q$. Video: $\bf{f'}$. Assistant:}
\end{tcolorbox}

A regular expression is applied to evaluate whether $o_i$ meets the specified requirements, thereby generating the format reward ${r'_i}$:
\begin{equation}
	r'_i = 
	\begin{cases} 
		1, & {\rm{if \: format \: is \: correct}}; \\ 
		0, & {\rm{if \: format \: is \: incorrect}}.
	\end{cases}
\end{equation}


\noindent \textbf{Accuracy Reward:}
The accuracy reward ${r''_i}$ model assesses whether the answer $a_i$ is semantically consistent with the ground truth $g$. For example, multiple-choice questions typically have precise and unique answers, which can be easily extracted when the response adheres to the specified format.
\begin{equation}
	r''_i = 
	\begin{cases} 
		1, & {a_i} = g; \\ 
		0, & {a_i} \ne g.
	\end{cases}
\end{equation}


\noindent \textbf{Logical Consistency Reward:}
When using only the format reward and accuracy reward, we consistently observed hacking behaviors. Specifically, for spatial reasoning tasks where the possible answers are limited (e.g., the relative position of an object with respect to the agent's body), cases arise where an incorrect reasoning process $p_i$ leads to a correct answer $a_i$, which is mistakenly assigned a positive reward. As such cases accumulate, the logical consistency of the model's responses deteriorates. To address this issue, we introduce a simple yet effective process reward. Our goal is to ensure a lower bound on logical consistency, such that the reasoning ability of \(\pi_\theta\) should not degrade below that of the reference model \(\pi_{\text{ref}}\). Therefore, when the model's answer is correct ($a_i = g$), we input the question $q$ and reasoning process $p_i$ into the reference model without providing video frames, yielding an answer: 
\begin{equation}
	{a'_i} \sim {\pi _{{\rm{ref}}}}\left( {a|q,{p_i}} \right).
\end{equation}
If ${a'_i}$ is consistent with $a_i$, it indicates that the reasoning process can logically lead to the answer; otherwise, it reflects a logical inconsistency between the reasoning process and the answer.
\begin{equation}
	r'''_i = 
	\begin{cases} 
		1, & a_i = a'_i = g; \\ 
		0, & \text{else}.
	\end{cases}
\end{equation}

\noindent \textbf{Total Reward:}
The total reward is a linear combination of the three rewards mentioned above:
\begin{equation}
	{r_i} = {\omega _1}{r'_i} + {\omega _2}{r''_i} + {\omega _3}{r'''_i}.
\end{equation}



