\appendix
\section{Appendix}
\subsection{Dataset Introduction}
\textbf{UrbanVideo-Bench:}
UrbanVideo-Bench is one of the training and testing datasets designed for embodied reasoning (embodied-r). This benchmark was proposed by Tsinghua University in February 2025. It captures two embodied characteristics of urban environments: complex urban scenes featuring dynamic and static elements, and unique aerial navigation scenarios. The dataset consists of 4 categories and 16 tasks, aimed at evaluating Video-LLMs in terms of recall, perception, reasoning, and navigation capabilities. In our paper, we focus on 4 of these complex tasks for reinforcement learning in video-based learning: \textbf{Landmark Position}, \textbf{Counterfactual Reasoning}, \textbf{Progress Evaluation}, and \textbf{Action Generation}, which represent challenging embodied outdoor tasks.

\textbf{VSI-Bench:}
VSI-Bench is another training and testing dataset for embodied reasoning (embodied-r). Proposed by <PERSON><PERSON><PERSON><PERSON><PERSON>'s team at Stanford in December 2024, this benchmark provides high-quality evaluation metrics for assessing the 3D, video-based, visual-spatial intelligence of multimodal large language models (MLLMs). The dataset comprises 2 categories and 8 tasks designed to evaluate key aspects of spatial reasoning. In our paper, we focus on 4 tasks for reinforcement learning in video-based learning: \textbf{Relative Distance}, \textbf{Relative Direction}, \textbf{Route Planning}, and \textbf{Appearance Order}, all of which are categorized as challenging embodied outdoor tasks.

\textbf{EgoSchema:}
EgoSchema is one of the Out-of-Distribution (OOD) datasets utilized to evaluate the generalization capability of our model. This dataset is specifically designed as a long-form video question-answering benchmark, aimed at assessing modern vision and language systems' ability to understand and reason over extended video content. It provides a rigorous evaluation framework for long video understanding tasks.

\textbf{MVBench:}
MVBench is another Out-of-Distribution (OOD) dataset employed to test the generalization capability of our model. MVBench consists of 20 complex video tasks, offering a comprehensive benchmark for evaluating the video understanding capabilities of existing multimodal models. This dataset is designed to address diverse and challenging scenarios in video-based reasoning.

\subsection{Details of Key-Frame Extractor}

The goal of key-frame extraction is to ensure sufficient information gain between frames while maintaining a certain degree of overlap. The specific process is as follows: 

Step 1: a perspective transformation is used to model the geometric relationship between frames. Assuming \( f_t \) is a key-frame, to determine whether \( f_{t+1} \) should also be considered a keyframe, keypoints and descriptors are calculated from \( f_t \) and \( f_{t+1} \) using the Oriented FAST and Rotated BRIEF (ORB) algorithm:
\begin{equation}
\text{Keypoints}_t, \text{Descriptors}_t = \text{ORB}(f_t),
\end{equation}
\begin{equation}
\text{Keypoints}_{t+1}, \text{Descriptors}_{t+1} = \text{ORB}(f_{t+1}).
\end{equation}
Next, a feature matching algorithm, such as the Brute-Force Matcher, is applied to match the descriptors between the two frames, identifying corresponding keypoint pairs \( \mathbf{l}_t^{\text{key}} \) and \( \mathbf{l}_{t+1}^{\text{key}} \).
Using the matched keypoint pairs, the Random Sample Consensus (RANSAC) algorithm is employed to estimate the homography matrix \( \mathbf{M} \), which maps the content of \( f_{t+1} \) to the coordinate space of \( f_t \).

Step 2: The overlap ratio between two frames is then computed. Assuming the size of each video frame is \( w \times h \), for frames \( f_t \) and \( f_{t+1} \):
\( \mathbf{l}_t = \{[0,0], [w,0], [w,h], [0,h]\} \) represents the four corner points of \( f_t \);
\( \mathbf{l}_{t+1} = \{[0,0], [w,0], [w,h], [0,h]\} \) represents the four corner points of \( f_{t+1} \).
Using the homography matrix \( \mathbf{M} \), the corner points \( \mathbf{l}_{t+1} \) of \( f_{t+1} \) are transformed into the coordinate space of \( f_t \):
\(
\mathbf{l}'_{t+1, i} = \mathbf{M} \cdot \mathbf{l}_{t+1, i}
\),
where \( \mathbf{l}_{t+1, i} = [x, y, 1]^T \) represents the corner points of \( f_{t+1} \) in homogeneous coordinates, and \( \mathbf{l}'_{t+1, i} = [x', y', w']^T \) represents the transformed corner points. The transformed points are further normalized to recover 2D coordinates, resulting in a quadrilateral representing \( f_{t+1} \) in \( f_t \)'s space. In \( f_t \)'s coordinate space, there are two polygons:
Polygon \( L_t \)is defined by the corner points \( \mathbf{l}_t \) of \( f_t \);
Polygon \( L_{t+1}' \)is defined by the transformed corner points \( \mathbf{l}'_{t+1} \).
Thus, the overlap ratio \( c \) is defined as:
\begin{equation}
c = \frac{\text{Area}(L_t \cap L_{t+1}')}{\text{Area}_{\text{total}}}.
\end{equation}

If \( c \) is less than a predefined threshold \(\varepsilon \), it indicates significant visual changes between the frames, and \( f_{t+1} \) is marked as a key-frame. Otherwise, the algorithm proceeds to calculate the overlap ratio between \( f_t \) and \( f_{t+2} \). This process continues until a new key-frame is identified, which then becomes the reference for subsequent frames. 
Considering the effect of viewpoint changes, rotations (both horizontal and vertical) result in larger field-of-view variations, leading to more frames being recorded during these movements. If the indices of the extracted keyframes are denoted as \( {\bf{f}}' = \left[ f_{k_0}, f_{k_1}, \dots, f_{k_n} \right] \), the keyframe extraction process can be summarized as:
\begin{equation}
{\bf{f}}' = \text{K-Extractor}(\bf{f}).
\end{equation}

\subsection{Details of Data Preparation}
\subsubsection{Task Selection Criteria}


In our study, we carefully selected specific tasks that emphasize spatial reasoning capabilities during motion within three-dimensional physical space. The selection process was guided by several key considerations:

\textbf{Focus on Reasoning Processes:} We prioritized tasks that require deep cognitive processing rather than simple recognition or recall. As highlighted in the main text, embodied spatial reasoning involves complex spatio-temporal relationships where agents must discover object associations across frames and extract task-relevant semantics. For instance, navigation tasks require agents to infer their location from historical observations, construct mental maps, develop high-level plans, and determine specific actions—processes that demand sophisticated reasoning capabilities.

\textbf{Diversity in Spatial Contexts:} To ensure comprehensive evaluation, we selected tasks from both indoor (VSI-Bench) and outdoor (UrbanVideo-Bench) environments, providing diverse spatial contexts that test different aspects of embodied reasoning. This diversity is crucial for evaluating the generalizability of our approach across varying spatial scales and environmental complexities.

\textbf{Emphasis on Long Reasoning Chains:} We specifically targeted tasks characterized by long spatial reasoning chains and historically low accuracy rates. These challenging tasks better demonstrate the value of our "slow thinking" approach, which encourages thorough reasoning before generating responses—similar to how recent advances in mathematical and scientific reasoning have benefited from reinforcement learning techniques.

\textbf{Deterministic Evaluation:} All selected tasks were formulated as multiple-choice question-answering problems to ensure determinism in answers, facilitating both RL training and direct calculation of accuracy for performance evaluation.

\subsubsection{Question Filtering Methodology}


To ensure the quality and validity of our dataset, we implemented a rigorous question filtering process:

\textbf{Blind Testing Filter:} We first evaluated questions using an untrained 7B language model without video input (blind selection). Questions that could be correctly answered without visual information were identified as potentially problematic, as they might rely more on textual patterns or common knowledge rather than genuine spatial reasoning based on video content.

\textbf{SFT-based Filtering:} After conducting supervised fine-tuning (SFT) without video inputs, we analyzed which question types showed significant improvement in accuracy. Categories where the model's performance increased substantially without visual information were flagged for removal, as this indicated strong correlations between question text and answers that could be exploited without actual spatial reasoning.

\textbf{Correlation Analysis:} We specifically eliminated question types where:
\begin{itemize}
    \item The model could achieve high accuracy without accessing video content
    \item Performance improved dramatically after text-only SFT training
    \item Question-answer pairs exhibited strong textual patterns that could be exploited without spatial understanding
\end{itemize}

This filtering methodology ensured that our final dataset genuinely tests embodied spatial reasoning capabilities rather than linguistic pattern matching or prior knowledge exploitation. By removing questions with strong text-answer correlations, we created a more challenging and valid benchmark that requires models to truly understand spatial relationships from video content.

\subsection{RL Hyperparameters}
The reinforcement learning (RL) training of Embodied-R requires careful hyperparameter tuning to balance computational efficiency with model performance. We conducted extensive experiments to determine the optimal configuration for our collaborative framework. The key hyperparameters used in our RL training process are summarized in Table~\ref{tab:rl_hyperparams}. These settings were selected to ensure stable training while maximizing the model's embodied reasoning capabilities. Notably, we used a relatively small learning rate (5e-7) to prevent catastrophic forgetting and a moderate KL coefficient (0.001) to maintain proximity to the reference model while allowing sufficient exploration.
\begin{table}[h]
\centering
\caption{Hyperparameters used in reinforcement learning training of Embodied-R.}
\label{tab:rl_hyperparams}
\begin{tabular}{ll}
\toprule
\textbf{Hyperparameter} & \textbf{Value} \\
\midrule
Optimizer & AdamW \\
Learning Rate & 5e-7 \\
Temperature & 1.0 \\
Train Batch Size & 32 \\
Rollout Size & 8 \\
KL Coefficient & 0.001 \\
Maximum Response Length & 2048 \\
Input Length & 6144 \\
Training Epochs & 12 \\
\bottomrule
\end{tabular}
\end{table}

