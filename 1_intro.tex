\vspace{-0.3cm}
\section{Introduction}
\label{sec:intro}


On the path toward Artificial General Intelligence (AGI)~\cite{fei2022towards}, we hope that pre-trained foundation models can not only perform tasks such as dialogue and image understanding in the cyber world~\cite{achiam2023gpt,team2023gemini} but also develop human-like embodied spatial cognition in the three-dimensional physical world, enabling them to perceive, think, and move~\cite{aubin2022towards,liu2024aligning}. The fundamental way humans achieve spatial cognition is through continuous, dynamic visual observations, akin to video streams~\cite{intriligator2001spatial,liu2023visual}. For example, by observing their surroundings, humans can infer their position relative to nearby objects. Similarly, based on historical visual observations, humans can determine the actions they should take to reach a target destination.

Visual spatial cognition can be divided into two levels: perception and reasoning~\cite{wang2024picture}. Perception refers to ``what is seen", characterized by direct, low-level tasks such as object recognition, edge detection, or color differentiation~\cite{wang2024embodiedscan}. Reasoning, on the other hand, involves ``what is understood" and ``what actions to take", which are indirect and higher-level tasks requiring logical inference and knowledge integration~\cite{zhao2025urbanvideobench}. Examples of reasoning include ``Where did I come from?" (e.g., recalling historical movement trajectories~\cite{mu2023embodiedgpt}), ``Where am I?" (e.g., inferring the spatial relationships between nearby objects and distances~\cite{azuma2022scanqa}), and ``Where do I want to go?" (e.g., planning actions and deciding movements to reach a destination~\cite{chen2024embodied}). While most existing research focuses on improving the perception capabilities of foundation models~\cite{chen2024internvl,bai2025qwen2}, with notable progress, their spatial reasoning abilities remain limited~\cite{chen2024spatialvlm,yang2024thinking}, and methods for enhancement are largely unexplored.

Specifically, video-based spatial reasoning poses several challenges, as follows:  
\begin{itemize}[leftmargin=*]
	\item Reasoning is always built upon perception~\cite{foglia2013embodied,liu2024aligning}. For the studied problem, continuous visual observations impose higher demands on perception. Reasoning cannot be well achieved with faulty perceptions or hallucinations~\cite{wang2024haloquest}. It is challenging to reason when it is already hard to perceive from the videos.
	\item Video data naturally involves complex spatio-temporal relationships, requiring the discovery of object associations across frames and the extraction of semantics relevant to the reasoning task~\cite{fei2024video}. For instance, to navigate to a destination outside the current field of view, one must infer their location from historical visual observations, build a mental map of the environment, develop a high-level plan to determine the direction, and finally decide on specific actions to execute. Existing supervised fine-tuning (SFT) training methods lack supervision for the reasoning process, making it difficult to handle such reasoning tasks~\cite{zhao2025urbanvideobench}.
	\item Embodied visual observations have distinct characteristics. First, understanding disembodied videos, such as movies or TV shows, primarily emphasizes the content within the video, often from a broad and objective perspective~\cite{li2024mvbench}. In contrast, egocentric videos focus on understanding the relationship between the observer and the surrounding environment, often from a constrained first-person perspective~\cite{grauman2022ego4d}. Second, embodied continuous visual observations are generated over time, indicating that embodied perception should rely on sequential inputs rather than aggregating all visual observations for a single input after a prolonged period~\cite{liu2024iof}. Finally, due to the continuity of motion in the physical world, egocentric visual observations also exhibit spatial continuity, meaning there is significant redundancy and repetition between frames. Consequently, directly applying existing multimodal large language models (MLLMs) to embodied videos leads to issues, including loss of generalization and input token limits caused by excessive redundant frames~\cite{abdin2024phi,lin2023video}.
\end{itemize}



Recently, the impressive performance of OpenAI’s o1/o3~\cite{openai2024b} and DeepSeek-R1~\cite{guo2025deepseek} in solving complex reasoning problems(e.g., mathematics, coding, science, etc.) has drawn attention to reinforcement learning (RL) techniques. By incorporating the chain-of-thought (CoT) reasoning process into post-training, large language models (LLMs) demonstrate a "slow-thinking" mode, where they reason thoroughly before generating responses~\cite{team2025kimi,xie2025logic}. Inspired by this, we attempt to introduce ``slow thinking" into embodied video-based spatial reasoning tasks, as shown in Figure \ref{fig:task}.

This brings a new challenge: the trade-off between model size and computational cost. Existing studies suggest a strong correlation between multimodal understanding/perception capabilities and model size~\cite{xu2023mmbench,gao2024embodiedcity,chandrasegaran2024hourvideo}. Since reasoning builds on perception, larger vision-language foundation models should be used as the starting point for training. However, increasing model size leads to often unacceptable computational costs. Additionally, video inputs map to long token sequences, further raising computational demands. Is there a way to leverage the perception capabilities of large-scale models while developing embodied reasoning abilities at a lower computational cost?

Inspired by neuroscience~\cite{zilles2010centenary}, spatial perception and reasoning involve distinct brain regions: visual perception occurs in the visual areas of the occipital lobe~\cite{clarke1990occipital}, basic spatial understanding in the parietal lobe~\cite{fogassi2005parietal}, and complex spatial reasoning in the prefrontal cortex~\cite{donoso2014foundations}. This inspired the design of a collaborative framework with two main components: a large-scale vision-language model (VLM) for perception and a small-scale language model (LM) for reasoning. Based on the continuity of observations, we first propose a key-frame extractor to retain critical information while reducing computational costs. Using a VLM, we sequentially extract semantic information from the frames, which simulates real-world online reasoning while effectively managing the input token length of VLMs for long video inputs. Finally, the semantic information and reasoning question are fed into the small-scale language model, which outputs the reasoning process and final answers.
The small-scale language model is trained with RL, where the reward modeling not only incorporates rule-based rewards inspired by Deepseek-R1-Zero~\cite{guo2025deepseek} but, more importantly, introduces a novel reward for the logical consistency of the reasoning process. In the experiments, we explore seven research questions, covering the framework's performance, RL's role in activating embodied spatial reasoning, and out-of-distribution generalization capabilities.

In general, the main contributions of this paper are as follows:
\begin{itemize}[leftmargin=*]
	\item We propose a \textbf{collaborative} framework for large-scale and small-scale foundation models to address spatial reasoning in the video modality. By decoupling perception and reasoning, the framework leverages the perceptual strength of large-scale foundation models while efficiently enhancing the reasoning capabilities of smaller models in a computationally resource-friendly manner.
	\item This is \textbf{the first work to employ reinforcement learning (RL) to enhance the embodied spatial reasoning abilities of foundation models}. Specifically, we introduce a novel \textbf{logical consistency reward}, which improves the alignment between reasoning processes and generated answers.
	\item Our proposed Embodied-R achieves performance \textbf{comparable to state-of-the-art multimodal large language models (e.g., OpenAI-o1/Gemini-2.5-Pro)} on both in-distribution and out-of-distribution benchmarks. We further investigate \textbf{research questions including the generalization comparison between models trained by SFT \& RL, reward design strategies, etc}.
\end{itemize}






